import requests
import json
import time
import random
import re

class MeituanCommentsReply:
    def __init__(self, cookies=None):
        self.base_url = "https://waimaieapp.meituan.com/gw/customer/comment/reply"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Referer": "https://waimaieapp.meituan.com/frontweb/ffw/userComment_gw",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Host": "waimaieapp.meituan.com",
            "Origin": "https://waimaieapp.meituan.com",
            "Content-Type": "application/x-www-form-urlencoded",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin"
        }
        
        # 如果没有提供cookies，使用默认值
        if cookies is None:
            self.cookies = {
                "_lxsdk_cuid": "19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8",
                "token": "06JKjVDdvx48fVY266Q07U9uBYFMIAQI4j19FmCKU0GI*",
                "wmPoiId": "27260079",
                "acctId": "229540584",
                "region_id": "1000420600",
                "region_version": "1743386677"
            }
        else:
            self.cookies = cookies
        
        # 从cookie中提取必要的信息
        self.token = self.cookies.get("token")
        self.acct_id = self.cookies.get("acctId")
        self.wm_poi_id = self.cookies.get("wmPoiId")
        
        # 提取region_id和region_version
        self.region_id = self.cookies.get("region_id")
        self.region_version = self.cookies.get("region_version")
        
        # 如果cookie中没有直接的region_id和region_version，尝试从set_info中提取
        if (not self.region_id or not self.region_version) and "set_info" in self.cookies:
            try:
                set_info = json.loads(self.cookies.get("set_info", "{}"))
                self.region_id = self.region_id or set_info.get("region_id")
                self.region_version = self.region_version or set_info.get("region_version")
            except:
                pass
        
        # 确保必要的信息存在
        if not self.token or not self.acct_id or not self.wm_poi_id:
            raise ValueError("Cookie中缺少必要的信息: token, acctId 或 wmPoiId")
        
    def reply_comment(self, comment_id, comment_time, reply_text, wm_poi_id=None):
        """回复评论
        
        参数:
            comment_id: 评论ID
            comment_time: 评论时间，格式为 "YYYY-MM-DD"
            reply_text: 回复内容
            wm_poi_id: 商家ID，如果不提供则使用cookie中的值
        
        返回:
            成功返回True，失败返回False
        """
        # 使用参数提供的wmPoiId或cookie中的值
        wm_poi_id = wm_poi_id or self.wm_poi_id
        
        # 构建请求参数
        data = {
            "ignoreSetRouterProxy": "true",
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "2.4.0",
            "acctId": self.acct_id,
            "wmPoiId": wm_poi_id,
            "token": self.token,
            "appType": "3",
            "toCommentId": comment_id,
            "comment": reply_text,
            "userCommentCtime": comment_time
        }
        
        # 如果有region_id和region_version，添加到参数中
        if self.region_id:
            data["region_id"] = self.region_id
        if self.region_version:
            data["region_version"] = self.region_version
        
        # mtgsig参数可能需要动态生成，这里使用示例中的值并更新时间戳
        mtgsig = {
            "a1": "1.1",
            "a2": int(time.time() * 1000),
            "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
            "a5": "zNIpqKxo5S/4VRNgIVQI9W==",
            "a6": "hs1.4aOG4x69iuIGtADfqn9IKcYoqcroTMmwaQtJlKgg+rpRFmZBhsnWLzVklTh7gH7hBY2pnzCGYyxPLwkBIAKJl1Q==",
            "x0": 4,
            "d1": "469b128d7e2dba6db117084538b6a8cd"
        }
        data["mtgsig"] = json.dumps(mtgsig)
        
        try:
            response = requests.post(self.base_url, data=data, headers=self.headers, cookies=self.cookies)
            response.raise_for_status()
            result = response.json()
            
            if result.get("success") and result.get("code") == 0:
                print(f"回复成功: 评论ID {comment_id}")
                return True
            else:
                print(f"回复失败: {result.get('message')}")
                return False
        except Exception as e:
            print(f"请求失败: {e}")
            return False
    
    def batch_reply(self, comments, reply_template=None):
        """批量回复评论
        
        参数:
            comments: 评论列表，每个评论应包含 id, createTime, wmPoiId(可选) 字段
            reply_template: 回复模板，如果为None则使用默认模板
        
        返回:
            成功回复的数量
        """
        if not reply_template:
            reply_templates = [
                "感谢您的评价，我们会继续努力提供更好的服务！",
                "谢谢亲的好评，有您的鼓励我们会做的更好，期待您的再次光临！",
                "非常感谢您的支持与肯定，我们会一如既往地为您提供优质服务！",
                "感谢您的光临和评价，您的满意是我们最大的动力！",
                "谢谢您的评价，我们会继续努力提升服务质量，期待您的再次光临！"
            ]
        else:
            reply_templates = [reply_template]
        
        success_count = 0
        for comment in comments:
            # 随机选择一个回复模板
            reply_text = random.choice(reply_templates)
            
            # 获取评论的wmPoiId，如果存在的话
            wm_poi_id = comment.get("wmPoiId", None)
            
            # 回复评论
            if self.reply_comment(comment["id"], comment["createTime"], reply_text, wm_poi_id):
                success_count += 1
            
            # 添加随机延迟，避免被反爬
            time.sleep(random.uniform(2, 5))
        
        return success_count

# 解析cookie字符串为字典
def parse_cookies(cookies_str):
    """
    解析cookie字符串为字典
    
    参数:
        cookies_str: Cookie字符串
        
    返回:
        Cookie字典
    """
    cookie_dict = {}
    if not cookies_str:
        return cookie_dict
        
    for item in cookies_str.split(';'):
        if item.strip():
            try:
                key, value = item.strip().split('=', 1)
                cookie_dict[key.strip()] = value.strip()
            except ValueError:
                pass  # 忽略格式不正确的cookie项
    
    # 尝试从set_info中提取region_id和region_version
    if "set_info" in cookie_dict:
        try:
            set_info_str = cookie_dict.get("set_info", "{}")
            # 使用正则表达式提取region_id和region_version
            region_id_match = re.search(r'"region_id"\s*:\s*"([^"]+)"', set_info_str)
            region_version_match = re.search(r'"region_version"\s*:\s*(\d+)', set_info_str)
            
            if region_id_match and "region_id" not in cookie_dict:
                cookie_dict["region_id"] = region_id_match.group(1)
            if region_version_match and "region_version" not in cookie_dict:
                cookie_dict["region_version"] = region_version_match.group(1)
        except:
            pass
            
    return cookie_dict

# 示例用法
def reply_single_comment(cookies, comment_id, comment_time, reply_text="谢谢亲的好评，有您的鼓励我们会做的更好，期待您的再次光临嗒!!", wm_poi_id=None):
    """
    回复单条评论
    
    参数:
        cookies: Cookie字典或字符串
        comment_id: 评论ID
        comment_time: 评论时间，格式为 "YYYY-MM-DD"
        reply_text: 回复内容
        wm_poi_id: 商家ID，如果不提供则使用cookie中的值
        
    返回:
        成功返回True，失败返回False
    """
    # 如果cookies是字符串，转换为字典
    if isinstance(cookies, str):
        cookies = parse_cookies(cookies)
    
    # 创建回复实例
    replier = MeituanCommentsReply(cookies=cookies)
    
    # 回复评论
    return replier.reply_comment(comment_id, comment_time, reply_text, wm_poi_id)

def batch_reply_comments(cookies, comments, reply_template=None):
    """
    批量回复评论
    
    参数:
        cookies: Cookie字典或字符串
        comments: 评论列表，每个评论应包含 id, createTime, wmPoiId(可选) 字段
        reply_template: 回复模板
        
    返回:
        成功回复的数量
    """
    # 如果cookies是字符串，转换为字典
    if isinstance(cookies, str):
        cookies = parse_cookies(cookies)
    
    # 创建回复实例
    replier = MeituanCommentsReply(cookies=cookies)
    
    # 批量回复评论
    return replier.batch_reply(comments, reply_template)

if __name__ == "__main__":
    # 示例cookie字符串
    example_cookie = "_lxsdk_cuid=19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8; token=06JKjVDdvx48fVY266Q07U9uBYFMIAQI4j19FmCKU0GI*; wmPoiId=27260079; acctId=229540584; region_id=1000420600; region_version=1743386677"
    
    # 示例调用：回复单条评论
    success = reply_single_comment(
        cookies=example_cookie,
        comment_id="8835773814",
        comment_time="2025-07-01"
    )
    print(f"回复{'成功' if success else '失败'}") 