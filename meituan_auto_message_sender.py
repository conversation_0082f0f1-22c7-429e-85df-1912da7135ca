#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
美团自动消息发送脚本

此脚本用于自动向美团上的未回复聊天发送消息。
它会获取未回复的消息，并使用这些数据构建和发送回复。
"""

import requests
import json
import time
import asyncio
import random
import string
import websockets.client
import websockets.exceptions
from meituan_serializer import serialize_auth, serialize_request, serialize_trans_up
from meituan_buffer import hex_dump, array_buffer_to_base64
from meituan_unreplied_messages import get_unreplied_messages, parse_cookies
import collections.abc
from typing import Union, Sequence
import os
from datetime import datetime
REPLY_RECORD_FILE = "auto_reply_record.json"
RECENT_REPLIES_FILE = "recent_replies.json"


def generate_random_uuid(length=9):
    """生成指定长度的随机UUID"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length))

# 预定义的AUTH_DATA, REQUEST_DATA 和 TRANSUP_DATA模板
AUTH_DATA_TEMPLATE = {
    "version": "4.22.144",
    "passport": "",  # 将动态设置
    "password": "",  # 将动态设置
    "deviceid": "",  # 将从输入中设置
    "os": 4,
    "sdkVersion": 1,
    "deviceType": 4,
    "pushToken": "",
    "deviceData": "",
    "supportMultiDevices": True,
    "traceId": 0,
    "swimlane": ""
}

REQUEST_DATA_TEMPLATE = {
    "version": 1,
    "uri": 26869777,
    "deviceType": 4,
    "msgUuid": "",  # 将动态设置
    "msgId": "",
    "senderUid": "",  # 将从输入中设置
    "receiverUid": "",  # 将动态设置
    "receiverAppId": 0,
    "pubUid": "",  # 将动态设置
    "fromName": "",  # 将动态设置
    "cts": 0,  # 将更新为当前时间戳
    "pushType": 0,
    "direction": 1,
    "extension": "",  # 将动态设置
    "retries": 0,
    "toDeviceTypes": 0,
    "channel": 1001,
    "sessionSeqId": "",
    "compatible": "",
    "deviceId": "",  # 将从输入中设置
    "messageReceivedType": 7,
    "type": 1,
    "message": {
        "version": 1,
        "text": "好的",  # 默认消息，可自定义
        "font_name": "serif",
        "font_size": 12,
        "bold": False,
        "cipher_type": 0,
        "__type": 1
    }
}

TRANSUP_DATA_TEMPLATE = {
    "svid": 410,
    "uid": "",  # 将从输入中设置 (与senderUid相同)
    "flag": 0,
    "seqId": 0,
    "deviceId": "",  # 将从输入中设置
    "traceId": 0,
    "swimlane": ""
}

def log(message):
    """简单的带时间戳的日志记录"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

async def connect_and_send(auth_data, request_data, transup_data):
    """连接到服务器并发送消息"""
    log(f"正在连接到 wss://wmdxlwss.meituan.com...")
    try:
        websocket = await websockets.client.connect(
            "wss://wmdxlwss.meituan.com",
            ping_interval=30,
            ping_timeout=10,
            close_timeout=10,
            max_size=10 * 1024 * 1024,
            max_queue=32
        )
        # log("WebSocket连接已建立")
        
        # 启动消息接收任务
        receive_task = asyncio.create_task(receive_messages(websocket))
        
        # 发送认证数据
        # log("正在发送认证数据...")
        auth_result = serialize_auth(auth_data)
        # log(f"已发送 {auth_result['byteLength']} 字节的数据")
        await websocket.send(auth_result['bytes'])
        # log("认证数据已发送")

        # 短暂等待以确保收到认证响应
        await asyncio.sleep(1)

        # 更新请求数据中的时间戳
        request_data["cts"] = int(time.time() * 1000)

        # 发送TransUp消息
        # log(f"正在发送TransUp消息... (msgUuid: {request_data['msgUuid']})")
        transup_result = serialize_trans_up(request_data, transup_data)
        # log(f"已发送 {transup_result['byteLength']} 字节的数据")
        await websocket.send(transup_result['bytes'])
        # log("TransUp数据已发送")
        
        # 等待服务器响应
        await asyncio.sleep(1)
        
        # 关闭连接
        receive_task.cancel()
        try:
            await receive_task
        except asyncio.CancelledError:
            pass
        
        await websocket.close()
        log("WebSocket连接已关闭")

    except Exception as e:
        log(f"WebSocket错误: {e}")

async def receive_messages(websocket):
    """从服务器接收消息"""
    try:
        while True:
            message = await websocket.recv()
            
            # 处理二进制消息
            if isinstance(message, bytes):
                # log(f"收到二进制消息: {len(message)} 字节")

                # Base64输出
                base64_str = array_buffer_to_base64(message)
                # log(f"Base64: {base64_str[:50]}...")

                # 十六进制输出
                hex_str = hex_dump(message).split('\n')[0]
                # log(f"十六进制: {hex_str}")
            else:
                log(f"收到文本消息: {message}")
    except websockets.exceptions.ConnectionClosed:
        log("WebSocket连接已关闭")
    except asyncio.CancelledError:
        pass
    except Exception as e:
        log(f"接收消息出错: {e}")

def construct_extension(chat_data):
    """
    根据聊天数据构建请求数据中的extension字段，严格保证格式和示例一致（字段顺序、内容、转义）。
    """
    # 字段严格按示例顺序
    ext_dict = {
        "order_id": "0",
        "c_name": chat_data.get('title', 'C**'),
        "c_avatar_url": chat_data.get('icon', [''])[0] if chat_data.get('icon') else 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/2c0a215905dbed8df38292ced36fdabe/im_icon_customer.png',
        "poi_logo_url": chat_data.get('poiLogoUrl', ''),
        "user_id": str(chat_data.get('userId', '0')),
        "poi_name": chat_data.get('wmPoiName', ''),
        "main_uuid": "!ead860f5-29e5-4e03-811b-00a935f69de9",
        "im_role": 3,
        "poi_id": str(chat_data.get('wmPoiId', '0')),
        "poi_id_str": "DqduLlI9FGpjMB3Qd7pBpAI",
        "role_name": chat_data.get('wmPoiName', ''),
        "role_logo_url": chat_data.get('poiLogoUrl', ''),
        "role_phone_number": "",
        "poi_nickname": f"{chat_data.get('wmPoiName', '')}客服",
        "tailNumber": "",
        "source": "PC",
        "role_type": "4",
        "deviceId": "!ead860f5-29e5-4e03-811b-00a935f69de9",
        "poi_order_count": "门店新客"
    }
    # 先转成紧凑JSON
    ext_json = json.dumps(ext_dict, ensure_ascii=False, separators=(",", ":"))
    # 再全部转义双引号
    ext_json_escaped = ext_json.replace('"', '\\"')
    # 外层再加一对双引号，和示例完全一致
    return f'"{ext_json_escaped}"'

def construct_password(cookies):
    """使用模板3_{acctId}_{token}*从cookie构建密码"""
    acct_id = cookies.get('acctId', '0')
    token = cookies.get('token', '0')
    return f"3_{acct_id}_{token}"

def load_reply_record():
    if os.path.exists(REPLY_RECORD_FILE):
        with open(REPLY_RECORD_FILE, "r", encoding="utf-8") as f:
            try:
                return json.load(f)
            except Exception:
                return {}
    return {}

def save_reply_record(record):
    with open(REPLY_RECORD_FILE, "w", encoding="utf-8") as f:
        json.dump(record, f, ensure_ascii=False)

def has_replied_today(shop_id, title):
    today = datetime.now().strftime("%Y-%m-%d")
    record = load_reply_record()
    return record.get(today, {}).get(str(shop_id), {}).get(title, False)

def mark_replied_today(shop_id, title):
    today = datetime.now().strftime("%Y-%m-%d")
    record = load_reply_record()
    # 只保留当天，自动清理过期
    if today not in record:
        record = {today: {}}
    if str(shop_id) not in record[today]:
        record[today][str(shop_id)] = {}
    record[today][str(shop_id)][title] = True
    save_reply_record(record)

def load_recent_replies():
    """加载最近回复记录"""
    if os.path.exists(RECENT_REPLIES_FILE):
        with open(RECENT_REPLIES_FILE, "r", encoding="utf-8") as f:
            try:
                return json.load(f)
            except Exception:
                return {}
    return {}

def save_recent_replies(record):
    """保存最近回复记录"""
    with open(RECENT_REPLIES_FILE, "w", encoding="utf-8") as f:
        json.dump(record, f, ensure_ascii=False)

def has_replied_recently(shop_id, title, message_text, time_window=300):
    """检查是否在指定时间窗口内已回复过相同内容（默认5分钟）"""
    record = load_recent_replies()
    current_time = int(time.time())

    # 清理过期记录
    for key in list(record.keys()):
        if current_time - record[key].get('timestamp', 0) > time_window:
            del record[key]

    # 检查是否存在相同的回复
    reply_key = f"{shop_id}_{title}_{message_text}"
    if reply_key in record:
        return True

    return False

def mark_replied_recently(shop_id, title, message_text):
    """标记最近已回复"""
    record = load_recent_replies()
    current_time = int(time.time())

    # 清理过期记录（保留5分钟内的记录）
    for key in list(record.keys()):
        if current_time - record[key].get('timestamp', 0) > 300:
            del record[key]

    # 添加新记录
    reply_key = f"{shop_id}_{title}_{message_text}"
    record[reply_key] = {
        'timestamp': current_time,
        'shop_id': shop_id,
        'title': title,
        'message': message_text
    }

    save_recent_replies(record)

def auto_send_messages_to_unreplied(
    cookies,
    sender_uid,
    device_id,
    message_text: Union[str, Sequence[str]] = "好的"
):
    """
    自动向美团上的未回复消息发送回复。

    参数:
        cookies: 作为字符串或字典的cookie
        sender_uid: 消息的发送者UID
        device_id: 用于认证和消息发送的设备ID
        message_text: 可以是字符串或字符串列表，作为回复内容

    返回:
        成功回复的聊天数量
    """
    if isinstance(cookies, str):
        cookies = parse_cookies(cookies)
    unreplied_chats = get_unreplied_messages(cookies)

    if not unreplied_chats:
        return 0

    import collections.abc
    if isinstance(message_text, collections.abc.Sequence) and not isinstance(message_text, str):
        message_texts = list(message_text)
    else:
        message_texts = [message_text]

    import copy
    auth_data = AUTH_DATA_TEMPLATE.copy()
    auth_data['deviceid'] = device_id
    auth_data['password'] = construct_password(cookies)

    transup_data = TRANSUP_DATA_TEMPLATE.copy()
    transup_data['uid'] = sender_uid
    transup_data['deviceId'] = device_id

    success_count = 0
    for chat in unreplied_chats:
        shop_id = chat.get('wmPoiId', '0')
        title = chat.get('title', '')

        # 2. 检查最后一条消息是否包含自动回复内容
        last_msg = chat.get('lastMsg', '')
        # log(f"检查聊天 {title} 的最后消息: {last_msg}")
        # log(f"自动回复消息模板: {message_texts}")

        # 改进匹配逻辑：检查是否包含关键词或完全匹配
        is_auto_reply = False
        if last_msg:
            # 检查是否完全匹配或包含主要内容
            for msg in message_texts:
                if msg in last_msg or last_msg in msg:
                    is_auto_reply = True
                    break
            # 额外检查一些常见的自动回复关键词
            auto_reply_keywords = ['商家如没有及时回复', '可能正在忙', '拨打商家后台电话', '直接联系哟']
            if any(keyword in last_msg for keyword in auto_reply_keywords):
                is_auto_reply = True

        if is_auto_reply:
            # log(f"最近一条消息已包含自动回复内容，跳过 {title}")
            continue

        # log(f"正在回复标题为 {title} 的聊天")
        reply_text = random.choice(message_texts)

        auth_data['passport'] = str(chat.get('wmPoiId', '0'))
        request_data = copy.deepcopy(REQUEST_DATA_TEMPLATE)
        request_data['senderUid'] = sender_uid
        request_data['deviceId'] = device_id
        request_data['receiverUid'] = str(chat.get('peerDxId', '0'))
        request_data['pubUid'] = str(chat.get('chatId', '0'))
        request_data['fromName'] = chat.get('wmPoiName', '')
        request_data['extension'] = construct_extension(chat)
        request_data['msgUuid'] = generate_random_uuid()
        request_data['message']['text'] = reply_text

        try:
            asyncio.run(connect_and_send(auth_data, request_data, transup_data))
            # log(f"消息已发送给 {title}，内容：{reply_text}")
            # 标记为已回复
            mark_replied_today(shop_id, title)
            mark_replied_recently(shop_id, title, reply_text)
            success_count += 1
            time.sleep(0.2)
        except Exception as e:
            log(f"发送消息给 {title} 失败: {e}")

    return success_count

if __name__ == "__main__":
    # 使用示例
    example_cookie = "_lxsdk_cuid=19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8; uuid=7d87f1bd424c40c68858.1733153057.1.0.0; swim_line=default; utm_source_rg=; userId=; iuuid=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; device_uuid=!ead860f5-29e5-4e03-811b-00a935f69de9; uuid_update=true; _lxsdk=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; pushToken=0phE1U0H6Qkk_lD_dkMN_qQYm0SCZHc7UVkkP_TRhhHo*; _ga_95GX0SH5GM=GS2.1.s1751944327$o3$g0$t1751944327$j60$l0$h0; _ga=GA1.1.712315181.1733152796; _ga_FSX5S86483=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; _ga_LYVVHCWVNG=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; wm_order_channel=sjzxpc; utm_source=60376; acctId=229540584; token=0_n_-tXcTlprI0Mqdd74rqhEVbdGDIPFoqZHe97IjrpU*; wmPoiId=27260079; city_id=420600; isChain=0; ignore_set_router_proxy=false; region_id=1000420600; region_version=1743386677; bsid=Y1saq6a-0e5E6JkvPBVyMx3pjDA-5a1SwZKA9ZLOzujWv9dbpx3BjFFi8YweUmCr-gkMIY6_eRbdf8QS588ksw; city_location_id=420600; location_id=420606; has_not_waimai_poi=0; onlyForDaoDianAcct=0; cityId=410100; provinceId=410000; scIndex=0; set_info_single=%7B%22regionIdForSingle%22%3A%************%22%2C%22regionVersionForSingle%22%3A1743386677%7D; shopCategory=food; JSESSIONID=dm4zvj8y1wxfxbxbcy3u6w2b; _lx_utm=utm_source%3Dbing%26utm_medium%3Dorganic; WEBDFPID=5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4-1753022982509-1733153579225QUUSWESfd79fef3d01d5e9aadc18ccd4d0c95075458; isOfflineSelfOpen=0; set_info=%7B%22wmPoiId%22%3A%2227260079%22%2C%22region_id%22%3A%************%22%2C%22region_version%22%3A1743386677%7D; wpush_server_url=wss://wpush.meituan.com; logan_session_token=3xbrhp4abtghmjzelp0k; setPrivacyTime=3_20250719; _lxsdk_s=19823298929-aca-45b-90b%7C%7C109"
    auto_send_messages_to_unreplied(
        cookies=example_cookie,
        sender_uid="3441625931",
        device_id="!ead860f5-29e5-4e03-811b-00a935f69de9",
        message_text=["好的", "收到，感谢您的反馈", "请稍等，马上处理"]
    ) 