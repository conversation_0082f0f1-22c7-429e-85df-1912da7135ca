# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 打包drivers目录下的chromedriver.exe
added_files = [
    ('drivers/chromedriver.exe', 'drivers'),  # 保持原始目录结构
    ('meituan_login_cdp.py', '.'),
]

a = Analysis(
    ['meituan_gui_login_modern.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        'customtkinter',
        'requests',
        'json',
        'pickle',
        'threading',
        'tkinter'
        'selenium',
        'webdriver_manager',  # 虽然不用但建议保留
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 这里可以加你不需要的包，比如 'PyQt5', 'PySide2' 等
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(
    a.pure, 
    a.zipped_data,
    cipher=block_cipher
)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='代理',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico', 
) 