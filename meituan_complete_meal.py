import requests
import json
import urllib.parse

def complete_meal_time(wm_order_view_id, wm_poi_id, cookie):
    """
    请求美团出餐时间接口
    
    参数:
        wm_order_view_id: 订单ID
        wm_poi_id: 商户ID
        cookie: 完整的cookie字符串
    
    返回:
        响应结果的JSON数据
    """
    # 请求URL
    url = "https://e.waimai.meituan.com/v2/common/w/reported/completeMealTime"
    
    # 查询参数
    params = {
        "region_id": "1000440100",
        "region_version": "1651046402",
        "yodaReady": "h5",
        "csecplatform": "4",
        "csecversion": "3.2.1",
        "mtgsig": '{"a1":"1.2","a2":1752469097444,"a3":"5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4","a5":"tikCXPlcaIZq0OSxG+jeY4nQ4O/kl6BfGGEPU5gQcIE+kheBVV0U","a6":"hs1.6W4irPBcoPdU0BGi4bsw5TOVPvbWdTLx0EIbrlHg3TjgYj0c7jD9YRZNjqB1bofTAeA3j0eUrQwO8TLadFtwMBf9+uxda+82WaJorfCJZo79n3tQ1MxWkGC+/X0m9v80Z","a8":"e087fbcc53a40c07f89b60f06c7503f9","a9":"3.2.1,7,162","a10":"a6","x0":4,"d1":"aa0b00a65906850bf2b7880ff30d84b8"}'
    }
    
    # 请求头
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/x-www-form-urlencoded",
        "cookie": cookie,
        "origin": "https://e.waimai.meituan.com",
        "referer": "https://e.waimai.meituan.com/new_fe/orderbusiness",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }
    
    # 请求体数据
    data = {
        "wmPoiId": wm_poi_id,
        "wmOrderViewId": wm_order_view_id
    }
    
    # 发送POST请求
    response = requests.post(
        url=url + "?" + urllib.parse.urlencode(params),
        headers=headers,
        data=data
    )
    
    # 检查响应状态
    if response.status_code == 200:
        try:
            return response.json()
        except json.JSONDecodeError:
            return {"error": "解析响应JSON失败", "text": response.text}
    else:
        return {"error": f"请求失败，状态码: {response.status_code}", "text": response.text}


def main():
    """测试函数"""
    # 测试参数
    wm_order_view_id = "3301685594250082734"
    wm_poi_id = "14551304"
    
    # 这里使用示例cookie，实际使用时应该替换为有效的cookie
    cookie = "_lxsdk_cuid=19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8; uuid=7d87f1bd424c40c68858.1733153057.1.0.0; wm_order_channel=default; swim_line=default; utm_source=; utm_source_rg=; userId=; iuuid=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; device_uuid=!ead860f5-29e5-4e03-811b-00a935f69de9; uuid_update=true; _lxsdk=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; pushToken=0phE1U0H6Qkk_lD_dkMN_qQYm0SCZHc7UVkkP_TRhhHo*; _ga_95GX0SH5GM=GS2.1.s1751944327$o3$g0$t1751944327$j60$l0$h0; _ga=GA1.1.712315181.1733152796; _ga_FSX5S86483=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; _ga_LYVVHCWVNG=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; _lx_utm=utm_source%3Dbing%26utm_medium%3Dorganic; WEBDFPID=5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4-1752547021091-1733153579225QUUSWESfd79fef3d01d5e9aadc18ccd4d0c95075458; shopCategory=food; JSESSIONID=5liedlwzpg93f3w85zf3dwan; acctId=126059518; token=0fCj41S-9Yk2n_1Ek3AHEItTMuK5zMNlH6fEwesk9Kdg*; wmPoiId=14551304; isOfflineSelfOpen=0; city_id=440100; isChain=0; ignore_set_router_proxy=false; region_id=1000440100; region_version=1651046402; bsid=-RIb_M7qF9swNCZ4_WeNaUwEMUvCsehCvoz_BLDtJ_zvuTJad9Z88zQRbG52t80ipQwFGp2-JBVb33TBzCcRAg; city_location_id=440100; location_id=440106; has_not_waimai_poi=0; onlyForDaoDianAcct=0; cityId=410900; provinceId=410000; scIndex=0; set_info_single=%7B%22regionIdForSingle%22%3A%221000440100%22%2C%22regionVersionForSingle%22%3A1651046402%7D; set_info=%7B%22wmPoiId%22%3A%2214551304%22%2C%22region_id%22%3A%221000440100%22%2C%22region_version%22%3A1651046402%7D; wpush_server_url=wss://wpush.meituan.com; setPrivacyTime=1_20250714; logan_session_token=pyl8m0x5jaio077i7k2b; _lxsdk_s=19807246ec8-1ee-c62-902%7C229540584%7C12994"
    
    # 调用函数
    result = complete_meal_time(wm_order_view_id, wm_poi_id, cookie)
    
    # 打印结果
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main() 