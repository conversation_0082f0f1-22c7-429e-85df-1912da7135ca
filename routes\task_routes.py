#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务管理路由
"""

import threading
import time
from flask import Blueprint, jsonify
from utils.db_utils import get_connection
import queue
import uuid
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

task_bp = Blueprint('task', __name__)

# 定时任务线程
scheduler_thread = None
scheduler_running = False

# 三个任务队列和线程
reply_queue = queue.Queue()
comment_queue = queue.Queue()
meal_queue = queue.Queue()
reply_thread = None
comment_thread = None
meal_thread = None

# 线程池全局声明 - 极度优化网络带宽使用（适配低带宽环境）
reply_executor = ThreadPoolExecutor(max_workers=2)  # 自动回复，极低并发适配低带宽
comment_executor = ThreadPoolExecutor(max_workers=2)  # 自动回评，极低并发适配低带宽
meal_executor = ThreadPoolExecutor(max_workers=1)  # 自动出餐，单线程处理减少网络冲突

# 自动回复分发节流控制
last_reply_dispatch_time = 0
REPLY_DISPATCH_INTERVAL = 10  # 秒，增加到10秒减少网络压力

# 任务错峰处理 - 避免所有任务同时执行
COMMENT_DISPATCH_INTERVAL = 15  # 自动回评间隔15秒
MEAL_DISPATCH_INTERVAL = 20     # 自动出餐间隔20秒
last_comment_dispatch_time = 0
last_meal_dispatch_time = 0
# 记录正在执行的店铺，避免重复执行（改为线程安全的锁）
import threading
executing_shops_lock = threading.Lock()
executing_shops = set()
# 记录每个店铺最后执行自动回复的时间
shop_last_reply_time = {}
shop_last_reply_time_lock = threading.Lock()
# 统计信息
task_stats = {
    'auto_reply': {'success': 0, 'skip': 0, 'error': 0},
    'auto_comment': {'success': 0, 'error': 0},
    'auto_meal': {'success': 0, 'error': 0}
}
last_stats_print_time = 0

# 自动回复并发worker
def reply_worker_batch(tasks):
    for task in tasks:
        shop_id = task['shop_id']
        current_time = time.time()

        # 线程安全地检查该店铺是否正在执行
        with executing_shops_lock:
            if shop_id in executing_shops:
                task_stats['auto_reply']['skip'] += 1
                continue

        # 线程安全地检查该店铺距离上次执行是否已超过间隔时间
        with shop_last_reply_time_lock:
            last_time = shop_last_reply_time.get(shop_id, 0)
            if current_time - last_time < REPLY_DISPATCH_INTERVAL:
                task_stats['auto_reply']['skip'] += 1
                continue

        # 线程安全地标记店铺为执行中
        with executing_shops_lock:
            executing_shops.add(shop_id)
        with shop_last_reply_time_lock:
            shop_last_reply_time[shop_id] = current_time

        try:
            from meituan_auto_message_sender import auto_send_messages_to_unreplied, parse_cookies
            cookies = parse_cookies(task['cookie'])

            # 记录开始时间
            start_time = time.time()

            # 执行自动回复
            success_count = auto_send_messages_to_unreplied(
                cookies=cookies,
                sender_uid=task['sender_uid'],
                device_id=task['device_id'],
                message_text=task['message_texts']
            )

            # 记录执行结果 - 只有当成功回复数量大于0时才记录日志
            if success_count > 0:
                task_stats['auto_reply']['success'] += 1
                end_time = time.time()
                execution_time = end_time - start_time

                # 记录日志到数据库
                try:
                    connection = get_connection()
                    cursor = connection.cursor()
                    from datetime import datetime
                    now = datetime.now()

                    # 清理3天前的日志
                    cursor.execute("DELETE FROM shop_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 DAY)")

                    # 插入日志记录
                    log_message = f"自动回复任务执行完成，成功回复 {success_count} 个聊天，耗时: {execution_time:.2f}秒"
                    cursor.execute(
                        "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                        [task['shop_id'], task['shop_name'], 'auto_reply', log_message, now]
                    )
                    connection.commit()
                    cursor.close()
                    connection.close()
                except Exception as e:
                    print(f"[自动回复] 记录日志失败: {e}")

        except Exception as e:
            task_stats['auto_reply']['error'] += 1
            print(f"[自动回复] 店铺{task['shop_name']}任务异常: {e}")
            # 记录错误日志
            try:
                connection = get_connection()
                cursor = connection.cursor()
                from datetime import datetime
                now = datetime.now()

                log_message = f"自动回复任务执行失败: {str(e)}"
                cursor.execute(
                    "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                    [task['shop_id'], task['shop_name'], 'auto_reply', log_message, now]
                )
                connection.commit()
                cursor.close()
                connection.close()
            except Exception as log_e:
                print(f"[自动回复] 记录错误日志失败: {log_e}")
        finally:
            # 线程安全地从执行中的店铺集合中移除该店铺
            with executing_shops_lock:
                executing_shops.discard(shop_id)

# 自动回评并发worker
def comment_worker_batch(tasks):
    for task in tasks:
        try:
            from meituan_auto_reply import auto_reply_comments, parse_cookies
            cookies = parse_cookies(task['cookie'])
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行自动回评
            success_count = auto_reply_comments(
                cookies=cookies,
                good_texts=task['good_texts'],
                mid_texts=task['mid_texts'],
                bad_texts=task['bad_texts']
            )
            
            # 记录执行结果 - 只有当成功回复数量大于0时才记录日志
            if success_count > 0:
                task_stats['auto_comment']['success'] += 1
                end_time = time.time()
                execution_time = end_time - start_time
                
                # 记录日志到数据库
                try:
                    connection = get_connection()
                    cursor = connection.cursor()
                    from datetime import datetime
                    now = datetime.now()
                    
                    # 清理3天前的日志
                    cursor.execute("DELETE FROM shop_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 DAY)")
                    
                    # 插入日志记录
                    log_message = f"自动回评任务执行完成，成功回复 {success_count} 条评论，耗时: {execution_time:.2f}秒"
                    cursor.execute(
                        "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                        [task['shop_id'], task['shop_name'], 'auto_comment', log_message, now]
                    )
                    connection.commit()
                    cursor.close()
                    connection.close()
                except Exception as e:
                    print(f"[自动回评] 记录日志失败: {e}")

        except Exception as e:
            task_stats['auto_comment']['error'] += 1
            print(f"[自动回评] 店铺{task['shop_name']}任务异常: {e}")
            # 记录错误日志
            try:
                connection = get_connection()
                cursor = connection.cursor()
                from datetime import datetime
                now = datetime.now()

                log_message = f"自动回评任务执行失败: {str(e)}"
                cursor.execute(
                    "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                    [task['shop_id'], task['shop_name'], 'auto_comment', log_message, now]
                )
                connection.commit()
                cursor.close()
                connection.close()
            except Exception as log_e:
                print(f"[自动回评] 记录错误日志失败: {log_e}")

# 自动出餐并发worker
def meal_worker_batch(tasks):
    for task in tasks:
        try:
            from meituan_unprocessed_orders import auto_complete_meal, parse_cookies
            cookies = parse_cookies(task['cookie'])
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行自动出餐
            success_count, completed_orders = auto_complete_meal(
                cookies=cookies,
                meal_duration=task['meal_duration']
            )
            
            # 记录执行结果 - 只有当成功出餐数量大于0时才记录日志
            if success_count > 0:
                task_stats['auto_meal']['success'] += 1
                end_time = time.time()
                execution_time = end_time - start_time
                
                # 构建详细的订单信息日志
                order_details = []
                for i, order in enumerate(completed_orders, 1):
                    detail = (f"订单{i}:\n"
                             f"  订单号: {order['order_id']}\n"
                             f"  日序号: {order.get('wm_poi_order_dayseq', 'N/A')}\n"
                             f"  订单类型: {order.get('order_type', '普通订单')}\n"
                             f"  下单时间: {order['order_time']}\n"
                             f"  出餐时间: {order['complete_time']}\n"
                             f"  出餐用时: {order['meal_time_used']}秒")
                    if order.get('delivery_time'):
                        detail += f"\n  配送时间: {order['delivery_time']}"
                    order_details.append(detail)
                
                # 记录日志到数据库
                try:
                    connection = get_connection()
                    cursor = connection.cursor()
                    from datetime import datetime
                    now = datetime.now()
                    
                    # 清理3天前的日志
                    cursor.execute("DELETE FROM shop_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 DAY)")
                    
                    # 构建日志消息
                    log_message = f"自动出餐任务执行完成，成功出餐 {success_count} 个订单，耗时: {execution_time:.2f}秒"
                    if order_details:
                        log_message += f"\n\n订单详情:\n" + "\n".join(order_details)
                    
                    cursor.execute(
                        "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                        [task['shop_id'], task['shop_name'], 'auto_meal', log_message, now]
                    )
                    connection.commit()
                    cursor.close()
                    connection.close()
                except Exception as e:
                    print(f"[自动出餐] 记录日志失败: {e}")
            # else:
            #     # 没有订单需要出餐时不记录日志，避免产生无意义的日志记录
            #     pass

        except Exception as e:
            task_stats['auto_meal']['error'] += 1
            print(f"[自动出餐] 店铺{task['shop_name']}任务异常: {e}")
            # 记录错误日志
            try:
                connection = get_connection()
                cursor = connection.cursor()
                from datetime import datetime
                now = datetime.now()

                log_message = f"自动出餐任务执行失败: {str(e)}"
                cursor.execute(
                    "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                    [task['shop_id'], task['shop_name'], 'auto_meal', log_message, now]
                )
                connection.commit()
                cursor.close()
                connection.close()
            except Exception as log_e:
                print(f"[自动出餐] 记录错误日志失败: {log_e}")

# --- 新增：定时检测cookie有效性线程 ---
def check_cookie_worker():
    while True:
        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            from datetime import datetime
            now = datetime.now()
            # 查询所有未过期的店铺
            cursor.execute("SELECT id, cookie FROM shops WHERE expire_time IS NULL OR expire_time > %s", [now])
            shops = cursor.fetchall()
            from meituan_login_cdp import MeituanLoginCDP
            checker = MeituanLoginCDP()
            for shop in shops:
                try:
                    # 解析cookie为list[dict]
                    cookies = []
                    for part in shop['cookie'].split(';'):
                        part = part.strip()
                        if not part: continue
                        if '=' in part:
                            k, v = part.split('=', 1)
                            cookies.append({'name': k.strip(), 'value': v.strip()})
                    info = checker.get_shop_info(cookies)
                    ck_status = 1 if info else 0
                    cursor.execute("UPDATE shops SET ck_status = %s WHERE id = %s", [ck_status, shop['id']])
                except Exception as e:
                    # print(f"检测店铺{shop['id']} cookie异常: {e}")  # 减少打印
                    cursor.execute("UPDATE shops SET ck_status = 0 WHERE id = %s", [shop['id']])
            connection.commit()
            cursor.close()
            connection.close()
        except Exception as e:
            print(f"[Cookie检测] 任务异常: {e}")
        time.sleep(600)  # 10分钟

# 主调度线程

def print_task_stats():
    """每5分钟打印一次任务统计信息"""
    global task_stats, last_stats_print_time
    current_time = time.time()

    # 每5分钟打印一次统计信息
    if current_time - last_stats_print_time >= 300:  # 300秒 = 5分钟
        # 添加线程池状态监控
        reply_active = reply_executor._threads.__len__() if hasattr(reply_executor, '_threads') else 0
        comment_active = comment_executor._threads.__len__() if hasattr(comment_executor, '_threads') else 0
        meal_active = meal_executor._threads.__len__() if hasattr(meal_executor, '_threads') else 0

        print(f"[任务统计] 自动回复: 成功{task_stats['auto_reply']['success']}, 跳过{task_stats['auto_reply']['skip']}, 错误{task_stats['auto_reply']['error']} (活跃线程:{reply_active}/2)")
        print(f"[任务统计] 自动回评: 成功{task_stats['auto_comment']['success']}, 错误{task_stats['auto_comment']['error']} (活跃线程:{comment_active}/2)")
        print(f"[任务统计] 自动出餐: 成功{task_stats['auto_meal']['success']}, 错误{task_stats['auto_meal']['error']} (活跃线程:{meal_active}/1)")
        print(f"[并发状态] 正在执行的店铺数: {len(executing_shops)}")

        # 重置统计计数器
        task_stats = {
            'auto_reply': {'success': 0, 'skip': 0, 'error': 0},
            'auto_comment': {'success': 0, 'error': 0},
            'auto_meal': {'success': 0, 'error': 0}
        }
        last_stats_print_time = current_time

def scheduler_loop(interval=15):  # 增加到15秒，减少网络压力
    global scheduler_running, last_reply_dispatch_time, last_comment_dispatch_time, last_meal_dispatch_time
    while scheduler_running:
        try:
            # 打印统计信息
            print_task_stats()

            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            from datetime import datetime
            now = datetime.now()
            # 1. 查所有需要的店铺 - 修改查询条件，排除 expire_time 为 null 的店铺
            query = '''
            SELECT id, shop_name, cookie, sender_uid, device_id, expire_time, ck_status,
                   auto_reply, reply_message_text,
                   auto_comment, auto_comment_types, comment_good_text, comment_mid_text, comment_bad_text,
                   auto_meal, auto_meal_periods
            FROM shops
            WHERE ck_status = 1
              AND expire_time IS NOT NULL
              AND expire_time > %s
              AND (auto_reply = 1 OR auto_comment = 1 OR auto_meal = 1)
            '''
            cursor.execute(query, [now])
            shops = cursor.fetchall()
            # 2. 查所有系统消息
            cursor.execute("SELECT * FROM system_messages WHERE is_active=1")
            all_sys_msgs = cursor.fetchall()
            # 3. 组装参数，分发任务
            # 自动回复任务：改为并发批处理
            import time as pytime
            now_ts = pytime.time()
            if now_ts - last_reply_dispatch_time >= REPLY_DISPATCH_INTERVAL:
                reply_tasks = []
                for shop in shops:
                    if shop['auto_reply']:
                        shop_id = shop['id']

                        # 线程安全地检查该店铺是否正在执行或距离上次执行时间不足
                        with executing_shops_lock:
                            if shop_id in executing_shops:
                                continue

                        with shop_last_reply_time_lock:
                            last_time = shop_last_reply_time.get(shop_id, 0)
                            if now_ts - last_time < REPLY_DISPATCH_INTERVAL:
                                continue

                        message_texts = []
                        if shop.get('reply_message_text'):
                            message_texts = [t.strip() for t in shop['reply_message_text'].replace('\r','').split('\n') if t.strip()]
                            if not message_texts:
                                message_texts = [shop['reply_message_text']]
                        if not message_texts:
                            message_texts = [msg['message_text'] for msg in all_sys_msgs if msg['message_type']=='auto_reply']
                        reply_tasks.append({
                            'shop_id': shop['id'],
                            'shop_name': shop['shop_name'],
                            'cookie': shop['cookie'],
                            'sender_uid': shop['sender_uid'],
                            'device_id': shop['device_id'],
                            'message_texts': message_texts
                        })

                # 分批分配到2个线程
                if reply_tasks:
                    batch_size = (len(reply_tasks) + 2 - 1) // 2  # 2个线程
                    for i in range(2):
                        batch = reply_tasks[i*batch_size:(i+1)*batch_size]
                        if batch:
                            reply_executor.submit(reply_worker_batch, batch)
                last_reply_dispatch_time = now_ts
            # 自动回评任务分批并发 - 错峰处理
            if now_ts - last_comment_dispatch_time >= COMMENT_DISPATCH_INTERVAL:
                comment_tasks = []
            for shop in shops:
                if shop['auto_comment']:
                    types = shop.get('auto_comment_types','good,mid,bad').split(',')
                    good_texts, mid_texts, bad_texts = [], [], []
                    if 'good' in types and shop.get('comment_good_text'):
                        good_texts = [t.strip() for t in shop['comment_good_text'].replace('\r','').split('\n') if t.strip()]
                    if 'mid' in types and shop.get('comment_mid_text'):
                        mid_texts = [t.strip() for t in shop['comment_mid_text'].replace('\r','').split('\n') if t.strip()]
                    if 'bad' in types and shop.get('comment_bad_text'):
                        bad_texts = [t.strip() for t in shop['comment_bad_text'].replace('\r','').split('\n') if t.strip()]
                    if 'good' in types and not good_texts:
                        good_texts = [msg['message_text'] for msg in all_sys_msgs if msg['message_type']=='auto_comment_good']
                    if 'mid' in types and not mid_texts:
                        mid_texts = [msg['message_text'] for msg in all_sys_msgs if msg['message_type']=='auto_comment_mid']
                    if 'bad' in types and not bad_texts:
                        bad_texts = [msg['message_text'] for msg in all_sys_msgs if msg['message_type']=='auto_comment_bad']
                    comment_tasks.append({
                        'shop_id': shop['id'],
                        'shop_name': shop['shop_name'],
                        'cookie': shop['cookie'],
                        'good_texts': good_texts if 'good' in types else None,
                        'mid_texts': mid_texts if 'mid' in types else None,
                        'bad_texts': bad_texts if 'bad' in types else None
                    })
            # 分批分配到2个线程
            if comment_tasks:
                batch_size = (len(comment_tasks) + 2 - 1) // 2 # 2个线程
                for i in range(2):
                    batch = comment_tasks[i*batch_size:(i+1)*batch_size]
                    if batch:
                        comment_executor.submit(comment_worker_batch, batch)
                last_comment_dispatch_time = now_ts
            # 自动出餐任务分批并发 - 错峰处理
            if now_ts - last_meal_dispatch_time >= MEAL_DISPATCH_INTERVAL:
                meal_tasks = []
            for shop in shops:
                if shop['auto_meal']:
                    import json
                    from datetime import datetime
                    meal_periods = []
                    if shop.get('auto_meal_periods'):
                        try:
                            meal_periods = json.loads(shop['auto_meal_periods'])
                        except Exception:
                            pass
                    def get_current_period_duration(periods):
                        now = datetime.now()
                        now_minutes = now.hour * 60 + now.minute

                        # 找到"全天"标签的默认duration
                        default_duration = 300
                        for period in periods:
                            if period.get('label') == '全天':
                                default_duration = period.get('duration', 300)
                                break

                        # 检查是否在非"全天"的自定义时间段内
                        for period in periods:
                            try:
                                # 跳过"全天"标签，只处理自定义时间段
                                if period.get('label') == '全天':
                                    continue

                                start_h, start_m = map(int, period['start'].split(':'))
                                end_h, end_m = map(int, period['end'].split(':'))
                                start_minutes = start_h * 60 + start_m
                                end_minutes = end_h * 60 + end_m

                                # 跨天处理
                                if start_minutes <= end_minutes:
                                    in_period = start_minutes <= now_minutes <= end_minutes
                                else:
                                    in_period = now_minutes >= start_minutes or now_minutes <= end_minutes

                                if in_period:
                                    # 在自定义时间段内，使用该时间段的duration
                                    return period.get('duration', default_duration)
                            except Exception:
                                continue

                        # 不在任何自定义时间段内，使用"全天"的默认duration
                        return default_duration
                    meal_duration = get_current_period_duration(meal_periods)
                    meal_tasks.append({
                        'shop_id': shop['id'],
                        'shop_name': shop['shop_name'],
                        'cookie': shop['cookie'],
                        'meal_duration': meal_duration
                    })
            if meal_tasks:
                # 单线程处理，避免网络冲突
                meal_executor.submit(meal_worker_batch, meal_tasks)
                last_meal_dispatch_time = now_ts
            cursor.close()
            connection.close()
        except Exception as e:
            print(f"[调度器] 定时任务执行异常: {e}")
        time.sleep(interval)

# --- 自动启动定时任务线程 ---
if not scheduler_running:
    scheduler_running = True
    scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
    scheduler_thread.start()
    # 自动回复现在使用线程池，不需要单独的worker线程
    # 不要再启动 comment_worker_batch 和 meal_worker_batch 的线程
    # 启动cookie检测线程
    cookie_check_thread = threading.Thread(target=check_cookie_worker, daemon=True)
    cookie_check_thread.start()

@task_bp.route('/start-scheduler', methods=['POST'])
def start_scheduler():
    """启动定时任务"""
    global scheduler_thread, scheduler_running
    if scheduler_running:
        return jsonify({'code': 1, 'message': '定时任务已在运行'})
    scheduler_running = True
    scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
    scheduler_thread.start()
    return jsonify({'code': 0, 'message': '定时任务已启动'})

@task_bp.route('/stop-scheduler', methods=['POST'])
def stop_scheduler():
    """停止定时任务"""
    global scheduler_running
    scheduler_running = False
    return jsonify({'code': 0, 'message': '定时任务已停止'}) 