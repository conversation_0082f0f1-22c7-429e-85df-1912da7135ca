#!/usr/bin/env python
# -*- coding: utf-8 -*-

from meituan_buffer import ByteBuffer
import random
import string
import time

# 缓冲区类型常量
class BufferType:
    STRING = 0
    BYTE = 1
    SHORT = 2
    BOOLEAN = 3
    INT = 4
    BYTES = 5
    LONG = 8
    ARRAY_LONG = 10
    ARRAY_ARRAY = 11
    PACKET = 100

# 消息常量
class MessageReceivedType:
    KEFU_MESSAGE = 7
    PUB_MESSAGE = 8

# Packet基类
class Packet:
    def __init__(self, data=None):
        """
        初始化Packet
        
        Args:
            data: 包含属性的字典
        """
        self.version = 1
        self.buffer_inited = False
        self._capacity = 1024
        self._position = 10
        self.from_unmarshall = False
        self.appid = 0  # 默认appid
        
        # 添加可能被子类使用的属性
        self.uri = 0
        self.mversion = 0
        self.crc32 = 0
        self.request_seq = 0
        self.extra = 0
        
        # 初始化子类特定属性
        self.init()
        
        # 复制传入的属性
        if data:
            for key, value in data.items():
                # 将JavaScript风格的属性名转换为Python风格
                py_key = key
                if key.startswith('_'):
                    py_key = key
                else:
                    py_key = key.replace('U', '_u').replace('I', '_i')
                    py_key = ''.join(['_' + c.lower() if c.isupper() else c for c in py_key]).lstrip('_')
                
                setattr(self, py_key, value)
        
        # 初始化后的处理
        self.init_after()
    
    def init(self):
        """
        初始化子类特定属性，由子类重写
        """
        pass
    
    def init_after(self):
        """
        初始化后的处理，由子类重写
        """
        pass
    
    def init_buffer(self):
        """
        初始化缓冲区
        
        Returns:
            bool: 初始化是否成功
        """
        if self.version > 1:
            self._position = 24  # 扩展包头长度
        
        self.buffer = ByteBuffer(self._capacity)
        self.buffer.position(self._position)
        return True
    
    def get_bytes(self):
        """
        获取序列化后的字节数组
        
        Returns:
            bytes: 序列化后的字节数组
        """
        self._marshall()
        return self.buffer.get_bytes()
    
    def _marshall(self):
        """
        序列化包头和内容
        """
        if not self.buffer_inited:
            self.buffer_inited = self.init_buffer()
        
        # 序列化具体消息内容
        self.marshall()
        
        # 写入包头
        length = self.buffer.position()
        self.buffer.put_int(length, 0)
        self.buffer.put_int(self.uri, 4)
        self.buffer.put_short(self.appid or 0, 8)
        
        if self.version > 1:
            self.buffer.put_short(self.mversion, 10)
            self.buffer.put_int(self.crc32, 12)
            self.buffer.put_int(self.request_seq, 16)
            self.buffer.put_int(self.extra, 20)
    
    def marshall(self):
        """
        序列化具体消息内容，由子类重写
        """
        pass
    
    # 辅助方法，用于序列化不同类型的数据
    def push(self, value, type_id=None):
        """
        根据类型序列化数据
        
        Args:
            value: 要序列化的值
            type_id: 数据类型，如果为None则自动推断
            
        Returns:
            None
        """
        if value is None:
            if type_id == BufferType.STRING:
                self._push_string('')
            elif type_id == BufferType.BYTES:
                self._push_bytes(bytes())
            elif type_id == BufferType.INT:
                self._push_int(0)
            elif type_id == BufferType.LONG:
                self._push_long(0)
            elif type_id == BufferType.SHORT:
                self._push_short(0)
            elif type_id == BufferType.BOOLEAN:
                self._push_bool(False)
            elif type_id == BufferType.BYTE:
                self._push_byte(0)
            else:
                print(f"未知类型: {type_id}")
            return
        
        if type_id is None:
            if isinstance(value, str):
                type_id = BufferType.STRING
            elif isinstance(value, int):
                type_id = BufferType.INT
            else:
                print(f"未指定类型且无法推断类型: {value}")
                return
        
        if type_id == BufferType.INT:
            self._push_int(value)
        elif type_id == BufferType.SHORT:
            self._push_short(value)
        elif type_id == BufferType.LONG:
            self._push_long(value)
        elif type_id == BufferType.STRING:
            self._push_string(value)
        elif type_id == BufferType.BOOLEAN:
            self._push_bool(value)
        elif type_id == BufferType.BYTE:
            self._push_byte(value)
        elif type_id == BufferType.BYTES:
            self._push_bytes(value)
        elif type_id == BufferType.PACKET:
            self._push_packet(value)
        else:
            print(f"不支持的类型: {type_id}")
    
    def _push_bool(self, value):
        """
        序列化布尔值
        
        Args:
            value: 布尔值
        """
        self.buffer.write_byte(1 if value else 0)
    
    def _push_byte(self, value):
        """
        序列化字节
        
        Args:
            value: 字节值
        """
        self.buffer.write_byte(value)
    
    def _push_short(self, value):
        """
        序列化短整数
        
        Args:
            value: 短整数值
        """
        self.buffer.put_short(value)
    
    def _push_int(self, value):
        """
        序列化整数
        
        Args:
            value: 整数值
        """
        self.buffer.put_int(value or 0)
    
    def _push_long(self, value):
        """
        序列化长整数
        
        Args:
            value: 长整数值
        """
        self.buffer.put_long(value or 0)
    
    def _push_string(self, value):
        """
        序列化字符串
        
        Args:
            value: 字符串值
        """
        if not value:
            value = ''
        
        length = self.buffer.write_utf8_string(value, self.buffer.write_offset + 2)
        self._push_short(length)
        self.buffer.position(self.buffer.write_offset + length)
    
    def _push_bytes(self, value):
        """
        序列化字节数组
        
        Args:
            value: 字节数组
        """
        if not value or len(value) == 0:
            self._push_short(0)
        else:
            self._push_short(len(value))
            self.buffer.put_bytes(value)
    
    def _push_packet(self, packet):
        """
        序列化Packet对象
        
        Args:
            packet: Packet对象
        """
        if packet:
            self._push_bytes(packet.get_bytes())
        else:
            self._push_bytes(bytes())


# 文本消息类
class TextMessage(Packet):
    def __init__(self, data=None):
        self.text = ''
        self.font_name = 'serif'
        self.font_size = 12
        self.bold = False
        self.cipher_type = 0
        self._type = 1  # 文本消息类型，改为单下划线避免名称改写
        super().__init__(data)
    
    def init(self):
        pass
    
    @property
    def __type(self):
        # 提供一个属性方法来访问_type
        return self._type
    
    def marshall(self):
        self.push(self.text)  # 默认STRING (0)
        self.push(self.font_name)  # 默认STRING (0)
        self.push(self.font_size)  # 默认INT (4)
        self.push(self.bold, BufferType.BOOLEAN)  # BOOLEAN (3)
        self.push(self.cipher_type, BufferType.SHORT)  # SHORT (2)
    
    def _marshall(self):
        if not self.buffer_inited:
            self.buffer_inited = self.init_buffer()
        
        # 序列化具体消息内容
        self.marshall()
        
        # 写入包头
        length = self.buffer.position()
        self.buffer.put_int(length, 0)
        
        # 写入特殊标识 01 9a 00 15 (在偏移量 4-7 的位置)
        self.buffer.buffer[4] = 0x01
        self.buffer.buffer[5] = 0x9a
        self.buffer.buffer[6] = 0x00
        self.buffer.buffer[7] = 0x15
        
        # 写入额外的标识 00 04 (在偏移量 8-9 的位置)
        self.buffer.buffer[8] = 0x00
        self.buffer.buffer[9] = 0x04


# 请求类
class Request(Packet):
    def __init__(self, data=None):
        super().__init__(data)
    
    def set_uid(self, uid):
        if hasattr(self, 'sender_uid'):
            self.sender_uid = uid
        elif hasattr(self, 'from_uid'):
            self.from_uid = uid


# 客服消息请求类
class PubSendMsgKFReq(Request):
    def __init__(self, data=None):
        # 先生成默认消息ID，如果data中有msg_uuid则会被覆盖
        self.msg_uuid = generate_msg_uuid()
        super().__init__(data)
        
        # 如果没有传入msg_uuid或传入的是空字符串，则使用生成的值
        if data and 'msgUuid' in data and data['msgUuid']:
            self.msg_uuid = data['msgUuid']
    
    def init(self):
        self.uri = 26869777  # URI_PUB_SEND_MSG_KF_REQ
        self.appid = 4       # 设置 appid 为 4
        self.device_type = 4  # 设备类型
        self.msg_id = ''
        self.sender_uid = 0
        self.receiver_uid = 0
        self.receiver_app_id = 0
        self.pub_uid = 0
        self.message = None
        self.from_name = ''
        self.cts = int(time.time() * 1000)  # 当前时间戳
        self.push_type = 0
        self.direction = 1
        self.extension = ''
        self.retries = 0
        self.to_device_types = 0
        self.channel = 0
        self.session_seq_id = ''
        self.compatible = ''
        self.device_id = ''
        self.message_received_type = MessageReceivedType.KEFU_MESSAGE
        self.type = None
    
    def init_after(self):
        if self.message and hasattr(self.message, '_type'):
            self.type = self.message._type
    
    def marshall(self):
        self.push(self.device_type, BufferType.BYTE)
        self.push(self.msg_uuid)  # 客户端消息ID
        self.push(self.msg_id, BufferType.LONG)
        self.push(self.sender_uid, BufferType.LONG)
        self.push(self.receiver_uid, BufferType.LONG)
        self.push(self.receiver_app_id, BufferType.SHORT)
        self.push(self.pub_uid, BufferType.LONG)
        
        # 修改对message.__type的访问方式
        if self.message and hasattr(self.message, '_type'):
            self._push_int(self.message._type)
        elif self.message and hasattr(self.message, '__type'):
            self._push_int(getattr(self.message, '__type'))
        else:
            self._push_int(1)  # 默认为文本消息类型
            
        self.push(self.message, BufferType.PACKET)
        self.push(self.from_name)
        self.push(self.cts, BufferType.LONG)
        self.push(self.push_type)
        self.push(self.direction, BufferType.BYTE)
        self.push(self.extension)
        self.push(self.retries, BufferType.BYTE)
        self.push(self.to_device_types, BufferType.BYTE)
        self.push(self.channel, BufferType.SHORT)
        self.push(self.session_seq_id, BufferType.LONG)
        self.push(self.compatible, BufferType.STRING)
        self.push(self.device_id, BufferType.STRING)


# TransUp 类 - 用于传输请求
class TransUp(Packet):
    def __init__(self, data=None):
        super().__init__(data)
    
    def init(self):
        self.uri = 1966080004  # URI_UNI_TRANSUP
        self.appid = 4        # 设置 appid 为 4
        self.svid = 0
        self.uid = 0
        self.buf = None       # 将包含 request 的序列化数据
        self.flag = 0
        self.seq_id = 0
        self.device_id = ''
        self.trace_id = 0
        self.swimlane = ''
    
    def marshall(self):
        self.push(self.svid, BufferType.SHORT)
        self.push(self.uid, BufferType.LONG)
        # 以上为 header 信息
        self.push(self.buf, BufferType.BYTES)
        self.push(self.flag, BufferType.BYTE)
        self.push(self.seq_id, BufferType.INT)
        self.push(self.device_id, BufferType.STRING)
        self.push(self.trace_id, BufferType.LONG)
        self.push(self.swimlane, BufferType.STRING)


# 认证数据包类
class AuthPacket(Packet):
    def __init__(self, data=None):
        # 确保version属性是整数类型
        self.version = 1  # 设置为整数版本号
        super().__init__(data)
    
    def init(self):
        self.uri = 196612  # 认证URI
        self.appid = 4
        self.version_str = "4.22.144"  # 使用version_str存储字符串版本号
        self.passport = ""
        self.password = ""
        self.deviceid = ""
        self.os = 4
        self.sdk_version = 1
        self.device_type = 4
        self.push_token = ""
        self.device_data = ""
        self.support_multi_devices = True
        self.trace_id = 0
        self.swimlane = ""
    
    def marshall(self):
        self._push_string(self.passport)
        self._push_string(self.password)
        self._push_string(self.deviceid)
        self._push_string(self.version_str)  # 使用version_str
        self._push_short(self.os)
        self._push_int(self.sdk_version)
        self._push_short(self.device_type)
        self._push_string(self.push_token)
        self._push_string(self.device_data)
        self._push_bool(self.support_multi_devices)
        self._push_long(self.trace_id)
        self._push_string(self.swimlane)


# 辅助函数
def generate_msg_uuid():
    """
    生成消息UUID，确保格式与官方一致
    
    Returns:
        str: 生成的消息UUID
    """
    # 生成以 "xpWrPe" 开头的ID，与5.html保持一致
    chars = string.ascii_letters + string.digits
    uuid = ''
    for _ in range(9):  # 只添加3个随机字符，总长度为9
        uuid += random.choice(chars)
    return uuid 