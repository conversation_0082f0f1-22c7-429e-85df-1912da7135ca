#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试cookie修复
"""

from meituan_unprocessed_orders import MeituanUnprocessedOrders

def test_cookie_parsing():
    """测试cookie解析功能"""
    print("🧪 测试cookie解析功能...")
    
    # 测试字符串cookie
    cookie_str = "region_id=1000420600; region_version=1743386677; token=test_token; wmPoiId=27260079"
    
    try:
        orders_handler = MeituanUnprocessedOrders(cookies=cookie_str)
        print(f"✅ region_id: {orders_handler.region_id}")
        print(f"✅ region_version: {orders_handler.region_version}")
        
        # 测试解析方法
        parsed = orders_handler._parse_cookie_string(cookie_str)
        print(f"✅ 解析结果: {parsed}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_dict_cookie():
    """测试字典cookie"""
    print("\n🧪 测试字典cookie...")
    
    cookie_dict = {
        "region_id": "1000420600",
        "region_version": "1743386677",
        "token": "test_token",
        "wmPoiId": "27260079"
    }
    
    try:
        orders_handler = MeituanUnprocessedOrders(cookies=cookie_dict)
        print(f"✅ region_id: {orders_handler.region_id}")
        print(f"✅ region_version: {orders_handler.region_version}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("🚀 Cookie处理修复测试")
    print("=" * 40)
    
    test_cookie_parsing()
    test_dict_cookie()
    
    print("\n" + "=" * 40)
    print("🎉 测试完成！")
