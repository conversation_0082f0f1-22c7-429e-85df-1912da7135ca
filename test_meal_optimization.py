#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试自动出餐优化功能
"""

from meituan_unprocessed_orders import auto_complete_meal
import json

def test_meal_optimization():
    """测试自动出餐优化功能"""
    print("🧪 测试自动出餐优化功能...")
    
    # 使用示例cookie（需要替换为真实有效的cookie）
    example_cookie = "_lxsdk_cuid=19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8; uuid=7d87f1bd424c40c68858.1733153057.1.0.0; swim_line=default; utm_source_rg=; userId=; iuuid=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; device_uuid=!ead860f5-29e5-4e03-811b-00a935f69de9; uuid_update=true; _lxsdk=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; pushToken=0phE1U0H6Qkk_lD_dkMN_qQYm0SCZHc7UVkkP_TRhhHo*; _ga_95GX0SH5GM=GS2.1.s1751944327$o3$g0$t1751944327$j60$l0$h0; _ga=GA1.1.712315181.1733152796; _ga_FSX5S86483=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; _ga_LYVVHCWVNG=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; wm_order_channel=sjzxpc; utm_source=60376; acctId=229540584; token=0_n_-tXcTlprI0Mqdd74rqhEVbdGDIPFoqZHe97IjrpU*; wmPoiId=27260079; city_id=420600; isChain=0; ignore_set_router_proxy=false; region_id=1000420600; region_version=1743386677; bsid=Y1saq6a-0e5E6JkvPBVyMx3pjDA-5a1SwZKA9ZLOzujWv9dbpx3BjFFi8YweUmCr-gkMIY6_eRbdf8QS588ksw; city_location_id=420600; location_id=420606; has_not_waimai_poi=0; onlyForDaoDianAcct=0; cityId=410100; provinceId=410000; scIndex=0; set_info_single=%7B%22regionIdForSingle%22%3A%************%22%2C%22regionVersionForSingle%22%3A1743386677%7D; JSESSIONID=dm4zvj8y1wxfxbxbcy3u6w2b; _lx_utm=utm_source%3Dbing%26utm_medium%3Dorganic; WEBDFPID=5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4-1753022982509-1733153579225QUUSWESfd79fef3d01d5e9aadc18ccd4d0c95075458; isOfflineSelfOpen=0; set_info=%7B%22wmPoiId%22%3A%2227260079%22%2C%22region_id%22%3A%************%22%2C%22region_version%22%3A1743386677%7D; setPrivacyTime=7_20250720; wpush_server_url=wss://wpush.meituan.com; logan_session_token=112phro6d6mrlyhpuanh; shopCategory=food; _lxsdk_s=1982acb1379-3ff-32c-84a%7C229540584%7C7"
    
    try:
        print("🚀 开始执行自动出餐测试...")
        success_count, completed_orders = auto_complete_meal(
            cookies=example_cookie,
            meal_duration=300  # 5分钟
        )
        
        print(f"✅ 测试完成！成功出餐 {success_count} 个订单")
        
        if completed_orders:
            print("\n📋 订单详情验证:")
            for i, order in enumerate(completed_orders, 1):
                print(f"\n  订单 {i}:")
                print(f"    订单号: {order.get('order_id', 'N/A')}")
                print(f"    日序号: {order.get('wm_poi_order_dayseq', 'N/A')}")
                print(f"    订单类型: {order.get('order_type', 'N/A')}")
                print(f"    基准时间: {order.get('order_time', 'N/A')}")
                print(f"    出餐时间: {order.get('complete_time', 'N/A')}")
                print(f"    出餐用时: {order.get('meal_time_used', 0)}秒")
                if order.get('delivery_time'):
                    print(f"    配送时间: {order.get('delivery_time')}")
                print(f"    是否预订单: {'是' if order.get('is_preorder') else '否'}")
                
                # 验证必要字段是否存在
                required_fields = ['order_id', 'wm_poi_order_dayseq', 'order_type']
                missing_fields = [field for field in required_fields if not order.get(field)]
                if missing_fields:
                    print(f"    ⚠️  缺少字段: {', '.join(missing_fields)}")
                else:
                    print(f"    ✅ 所有必要字段完整")
        else:
            print("📝 当前没有需要出餐的订单")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("💡 请确保cookie有效且网络连接正常")

def test_log_parsing():
    """测试日志解析功能"""
    print("\n🧪 测试日志解析功能...")
    
    # 模拟日志消息
    sample_log = """自动出餐任务执行完成，成功出餐 2 个订单，耗时: 15.32秒

订单详情:
订单1:
  订单号: 3001705621215774830
  日序号: 35
  订单类型: 预订单
  下单时间: 2025-01-28 11:09:46
  出餐时间: 2025-01-28 11:25:18
  出餐用时: 932秒
  配送时间: 2025-01-28 12:30:00

订单2:
  订单号: 3001705621215774831
  日序号: 36
  订单类型: 普通订单
  下单时间: 2025-01-28 11:15:22
  出餐时间: 2025-01-28 11:25:18
  出餐用时: 596秒"""
    
    print("📋 原始日志消息:")
    print(sample_log)
    
    print("\n🔍 解析结果:")
    
    # 模拟前端解析函数
    def extract_meal_count(log_message):
        import re
        match = re.search(r'成功出餐\s*(\d+)\s*个订单', log_message)
        return match.group(1) if match else '0'
    
    def extract_execution_time(log_message):
        import re
        match = re.search(r'耗时:\s*([\d.]+)秒', log_message)
        return match.group(1) + 's' if match else '-'
    
    def extract_order_details(log_message):
        details_index = log_message.find('订单详情:')
        if details_index == -1:
            return log_message
        details = log_message[details_index + 5:].strip()
        return re.sub(r'订单\d+:', r'\n\g<0>', details).strip()
    
    print(f"  出餐数量: {extract_meal_count(sample_log)}个")
    print(f"  执行耗时: {extract_execution_time(sample_log)}")
    print(f"  订单详情预览: {extract_order_details(sample_log)[:100]}...")

if __name__ == "__main__":
    print("🚀 自动出餐优化功能测试")
    print("=" * 60)
    
    # 测试自动出餐功能
    test_meal_optimization()
    
    # 测试日志解析功能
    test_log_parsing()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("\n📋 优化总结:")
    print("✅ 增加了 wm_poi_order_dayseq 字段提取")
    print("✅ 后端日志记录包含更详细的订单信息")
    print("✅ 前端日志显示优化为多列展示")
    print("✅ 支持预订单和普通订单的区分显示")
    print("✅ 日志解析功能完善，便于查看订单详情")
