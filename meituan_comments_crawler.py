import requests
import json
import pandas as pd
from datetime import datetime
import time
import random
import re
import urllib3
# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class MeituanCommentsCrawler:
    def __init__(self, cookies=None):
        self.base_url = "https://waimaieapp.meituan.com/gw/customer/comment/list"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Referer": "https://waimaieapp.meituan.com/frontweb/ffw/userComment_gw",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Host": "waimaieapp.meituan.com",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin"
        }
        
        # 如果没有提供cookies，使用默认值
        if cookies is None:
            self.cookies = {
                "_lxsdk_cuid": "19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8",
                "token": "06JKjVDdvx48fVY266Q07U9uBYFMIAQI4j19FmCKU0GI*",
                "wmPoiId": "27260079",
                "acctId": "229540584",
                "region_id": "1000420600",
                "region_version": "1743386677"
            }
        else:
            self.cookies = cookies
            
        # 从cookie中提取必要的信息
        self.token = self.cookies.get("token")
        self.acct_id = self.cookies.get("acctId")
        self.wm_poi_id = self.cookies.get("wmPoiId")
        
        # 提取region_id和region_version
        self.region_id = self.cookies.get("region_id")
        self.region_version = self.cookies.get("region_version")
        
        # 如果cookie中没有直接的region_id和region_version，尝试从set_info中提取
        if (not self.region_id or not self.region_version) and "set_info" in self.cookies:
            try:
                set_info = json.loads(self.cookies.get("set_info", "{}"))
                self.region_id = self.region_id or set_info.get("region_id")
                self.region_version = self.region_version or set_info.get("region_version")
            except:
                pass
        
        # 确保必要的信息存在
        if not self.token or not self.acct_id or not self.wm_poi_id:
            raise ValueError("Cookie中缺少必要的信息: token, acctId 或 wmPoiId")
        
    def get_comments(self, page_num=1, page_size=10):
        """获取评论数据"""
        params = {
            "ignoreSetRouterProxy": "true",
            "acctId": self.acct_id,
            "wmPoiId": self.wm_poi_id,
            "token": self.token,
            "appType": "3",
            "commScore": "0",
            "commType": "0",
            "hasContent": "-1",
            "periodType": "1",
            "beginTime": "1750089600",
            "endTime": "1752681600",
            "pageNum": str(page_num),
            "onlyAuditNotPass": "0",
            "pageSize": str(page_size),
            "source": "0",
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "2.4.0"
        }
        
        # 如果有region_id和region_version，添加到参数中
        if self.region_id:
            params["region_id"] = self.region_id
        if self.region_version:
            params["region_version"] = self.region_version
        
        # mtgsig参数可能需要动态生成，这里使用示例中的值
        params["mtgsig"] = json.dumps({
            "a1": "1.1",
            "a2": int(time.time() * 1000),
            "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
            "a5": "zNIpqOxo5S/4VRNgIVQI9W==",
            "a6": "hs1.4aOG4x69iuIGtADfqn9IKcYoqcroTMmwaQtJlKgg+rpRFmZBhsnWLzVklTh7gH7hBY2pnzCGYyxPLwkBIAKJl1Q==",
            "x0": 4,
            "d1": "dfad4aa009f35a6039270071d65e10f7"
        })
        
        try:
            response = requests.get(self.base_url, params=params, headers=self.headers, cookies=self.cookies)
            print(response)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"请求失败: {e}")
            return None
    
    def extract_comment_data(self, comment):
        """提取评论中的关键数据，返回id、wmPoiId和createTime"""
        return {
            "id": comment.get("id", ""),
            "wmPoiId": comment.get("wmPoiId", ""),
            "createTime": comment.get("createTime", "")
        }
    
    def crawl_all_comments(self, max_pages=10):
        """爬取所有评论数据，返回id、wmPoiId和createTime"""
        all_comments = []
        
        for page in range(1, max_pages + 1):
            print(f"正在爬取第 {page} 页...")
            result = self.get_comments(page_num=page)
            
            if not result or not result.get("success"):
                print(f"获取第 {page} 页数据失败")
                break
            
            comments = result.get("data", {}).get("list", [])
            if not comments:
                print("没有更多评论数据")
                break
            
            for comment in comments:
                comment_data = self.extract_comment_data(comment)
                all_comments.append(comment_data)
            
            total = result.get("data", {}).get("total", 0)
            if page * 10 >= total:
                print(f"已爬取所有评论，共 {total} 条")
                break
                
            # 添加随机延迟，避免被反爬
            time.sleep(random.uniform(1, 3))
        
        return all_comments

# 解析cookie字符串为字典
def parse_cookies(cookies_str):
    """
    解析cookie字符串为字典
    
    参数:
        cookies_str: Cookie字符串
        
    返回:
        Cookie字典
    """
    cookie_dict = {}
    if not cookies_str:
        return cookie_dict
        
    for item in cookies_str.split(';'):
        if item.strip():
            try:
                key, value = item.strip().split('=', 1)
                cookie_dict[key.strip()] = value.strip()
            except ValueError:
                pass  # 忽略格式不正确的cookie项
    
    # 尝试从set_info中提取region_id和region_version
    if "set_info" in cookie_dict:
        try:
            set_info_str = cookie_dict.get("set_info", "{}")
            # 使用正则表达式提取region_id和region_version
            region_id_match = re.search(r'"region_id"\s*:\s*"([^"]+)"', set_info_str)
            region_version_match = re.search(r'"region_version"\s*:\s*(\d+)', set_info_str)
            
            if region_id_match and "region_id" not in cookie_dict:
                cookie_dict["region_id"] = region_id_match.group(1)
            if region_version_match and "region_version" not in cookie_dict:
                cookie_dict["region_version"] = region_version_match.group(1)
        except:
            pass
            
    return cookie_dict

# 示例用法
def get_comments(cookies, max_pages=10):
    """
    获取美团外卖评论数据
    
    参数:
        cookies: Cookie字典或字符串
        max_pages: 最大爬取页数
        
    返回:
        评论列表
    """
    # 如果cookies是字符串，转换为字典
    if isinstance(cookies, str):
        cookies = parse_cookies(cookies)
    
    # 创建爬虫实例
    crawler = MeituanCommentsCrawler(cookies=cookies)
    
    # 爬取评论
    return crawler.crawl_all_comments(max_pages=max_pages)

if __name__ == "__main__":
    # 示例cookie字符串
    example_cookie = "_lxsdk_s=1984c6ade70-3ec-72c-ffd%7C%7C26; pushToken=01H6CmE4jO9ACX6CzfuVQfhq09pnOEFQvtqxeiaj2plw*; set_info=%7B%22wmPoiId%22%3A%2227124170%22%2C%22region_id%22%3A%************%22%2C%22region_version%22%3A1742440621%7D; set_info_single=%7B%22regionIdForSingle%22%3A%************%22%2C%22regionVersionForSingle%22%3A1742440621%7D; provinceId=410000; _lxsdk_cuid=1984c6ade6ec8-0df2ee3eb60172-26031d51-4b9600-1984c6ade6ec8; cityId=410900; region_version=1742440621; onlyForDaoDianAcct=0; _lxsdk=1984c6ade6ec8-0df2ee3eb60172-26031d51-4b9600-1984c6ade6ec8; region_id=1000320300; ignore_set_router_proxy=false; isChain=0; scIndex=0; acctId=227641160; city_id=320311; device_uuid=!715669da-6ce1-43f0-a3fb-4abdc539a12c; has_not_waimai_poi=0; location_id=320311; bsid=SgvG0hhR7Cfg7Ii3I3DaviFyMqXdG3ZMLR5VcuXIiYPHZbBWExBAs_Avm-yTaSKH2fpSzGOT0QwK6v9cu25Z8w; isOfflineSelfOpen=0; wmPoiId=27124170; JSESSIONID=8529kvxx2ebch0cfslcqah80; uuid_update=true; logan_session_token=l00r160h77yfrtwoa3eb; utm_source_rg=; token=01H6CmE4jO9ACX6CzfuVQfhq09pnOEFQvtqxeiaj2plw*; WEBDFPID=642473u9z25x5w9404u64z4485w9yxu38015x3zx00797958zw07w257-1753715129174-1753628725618EMAMOIMfd79fef3d01d5e9aadc18ccd4d0c95073757; shopCategory=food; wpush_server_url=wss://wpush.meituan.com; city_location_id=320300"
    
    # 示例调用
    comments = get_comments(cookies=example_cookie)
    print(f"共爬取 {len(comments)} 条评论")
    print(f"评论数据示例: {comments[:3] if comments else []}") 