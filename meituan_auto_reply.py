import requests
import json
import time
import random
import re
from datetime import datetime, timedelta

class MeituanCommentsCrawler:
    def __init__(self, cookies=None):
        self.base_url = "https://waimaieapp.meituan.com/gw/customer/comment/list"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Referer": "https://waimaieapp.meituan.com/frontweb/ffw/userComment_gw",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Host": "waimaieapp.meituan.com",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin"
        }
        
        if cookies is None:
            self.cookies = {
                "_lxsdk_cuid": "19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8",
                "token": "06JKjVDdvx48fVY266Q07U9uBYFMIAQI4j19FmCKU0GI*",
                "wmPoiId": "27260079",
                "acctId": "229540584",
                "region_id": "1000420600",
                "region_version": "1743386677"
            }
        else:
            self.cookies = cookies
            
        self.token = self.cookies.get("token")
        self.acct_id = self.cookies.get("acctId")
        self.wm_poi_id = self.cookies.get("wmPoiId")
        self.region_id = self.cookies.get("region_id")
        self.region_version = self.cookies.get("region_version")
        
        if (not self.region_id or not self.region_version) and "set_info" in self.cookies:
            try:
                set_info = json.loads(self.cookies.get("set_info", "{}"))
                self.region_id = self.region_id or set_info.get("region_id")
                self.region_version = self.region_version or set_info.get("region_version")
            except:
                pass
        
        if not self.token or not self.acct_id or not self.wm_poi_id:
            raise ValueError("Cookie中缺少必要的信息: token, acctId 或 wmPoiId")
        
    def get_comments(self, page_num=1, page_size=10, begin_time=None, end_time=None, comm_score=0):
        if begin_time is None:
            begin_time = int((datetime.now() - timedelta(days=30)).timestamp())
        if end_time is None:
            end_time = int(datetime.now().timestamp())
        
        params = {
            "ignoreSetRouterProxy": "true",
            "acctId": self.acct_id,
            "wmPoiId": self.wm_poi_id,
            "token": self.token,
            "appType": "3",
            "commScore": str(comm_score),
            "commType": "0",
            "hasContent": "-1",
            "periodType": "1",
            "beginTime": str(begin_time),
            "endTime": str(end_time),
            "pageNum": str(page_num),
            "onlyAuditNotPass": "0",
            "pageSize": str(page_size),
            "source": "0",
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "2.4.0"
        }
        
        if self.region_id:
            params["region_id"] = self.region_id
        if self.region_version:
            params["region_version"] = self.region_version
        
        params["mtgsig"] = json.dumps({
            "a1": "1.1",
            "a2": int(time.time() * 1000),
            "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
            "a5": "zNIpqOxo5S/4VRNgIVQI9W==",
            "a6": "hs1.4aOG4x69iuIGtADfqn9IKcYoqcroTMmwaQtJlKgg+rpRFmZBhsnWLzVklTh7gH7hBY2pnzCGYyxPLwkBIAKJl1Q==",
            "x0": 4,
            "d1": "dfad4aa009f35a6039270071d65e10f7"
        })
        
        try:
            response = requests.get(self.base_url, params=params, headers=self.headers, cookies=self.cookies)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"请求失败: {e}")
            return None

    def extract_comment_data(self, comment):
        return {
            "id": comment.get("id", ""),
            "wmPoiId": comment.get("wmPoiId", ""),
            "createTime": comment.get("createTime", "")
        }
    
    def crawl_all_comments(self, max_pages=10, begin_time=None, end_time=None, comm_score=0):
        all_comments = []
        
        for page in range(1, max_pages + 1):
            # print(f"正在爬取第 {page} 页...")  # 减少打印
            result = self.get_comments(page_num=page, begin_time=begin_time, end_time=end_time, comm_score=comm_score)

            if not result or not result.get("success"):
                # print(f"获取第 {page} 页数据失败")  # 减少打印
                break

            comments = result.get("data", {}).get("list", [])
            if not comments:
                # print("没有更多评论数据")  # 减少打印
                break
            
            for comment in comments:
                comment_data = self.extract_comment_data(comment)
                all_comments.append(comment_data)
            
            total = result.get("data", {}).get("total", 0)
            if page * 10 >= total:
                # print(f"已爬取所有评论，共 {total} 条")  # 减少打印
                break
                
            time.sleep(random.uniform(1, 3))
        
        return all_comments

class MeituanCommentsReply:
    def __init__(self, cookies=None):
        self.base_url = "https://waimaieapp.meituan.com/gw/customer/comment/reply"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Referer": "https://waimaieapp.meituan.com/frontweb/ffw/userComment_gw",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Host": "waimaieapp.meituan.com",
            "Origin": "https://waimaieapp.meituan.com",
            "Content-Type": "application/x-www-form-urlencoded",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin"
        }
        
        if cookies is None:
            self.cookies = {
                "_lxsdk_cuid": "19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8",
                "token": "06JKjVDdvx48fVY266Q07U9uBYFMIAQI4j19FmCKU0GI*",
                "wmPoiId": "27260079",
                "acctId": "229540584",
                "region_id": "1000420600",
                "region_version": "1743386677"
            }
        else:
            self.cookies = cookies
        
        self.token = self.cookies.get("token")
        self.acct_id = self.cookies.get("acctId")
        self.wm_poi_id = self.cookies.get("wmPoiId")
        self.region_id = self.cookies.get("region_id")
        self.region_version = self.cookies.get("region_version")
        
        if (not self.region_id or not self.region_version) and "set_info" in self.cookies:
            try:
                set_info = json.loads(self.cookies.get("set_info", "{}"))
                self.region_id = self.region_id or set_info.get("region_id")
                self.region_version = self.region_version or set_info.get("region_version")
            except:
                pass
        
        if not self.token or not self.acct_id or not self.wm_poi_id:
            raise ValueError("Cookie中缺少必要的信息: token, acctId 或 wmPoiId")
        
    def reply_comment(self, comment_id, comment_time, reply_text, wm_poi_id=None):
        wm_poi_id = wm_poi_id or self.wm_poi_id
        data = {
            "ignoreSetRouterProxy": "true",
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "2.4.0",
            "acctId": self.acct_id,
            "wmPoiId": wm_poi_id,
            "token": self.token,
            "appType": "3",
            "toCommentId": comment_id,
            "comment": reply_text,
            "userCommentCtime": comment_time
        }
        
        if self.region_id:
            data["region_id"] = self.region_id
        if self.region_version:
            data["region_version"] = self.region_version
        
        mtgsig = {
            "a1": "1.1",
            "a2": int(time.time() * 1000),
            "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
            "a5": "zNIpqKxo5S/4VRNgIVQI9W==",
            "a6": "hs1.4aOG4x69iuIGtADfqn9IKcYoqcroTMmwaQtJlKgg+rpRFmZBhsnWLzVklTh7gH7hBY2pnzCGYyxPLwkBIAKJl1Q==",
            "x0": 4,
            "d1": "469b128d7e2dba6db117084538b6a8cd"
        }
        data["mtgsig"] = json.dumps(mtgsig)
        
        try:
            response = requests.post(self.base_url, data=data, headers=self.headers, cookies=self.cookies)
            response.raise_for_status()
            result = response.json()
            
            if result.get("success") and result.get("code") == 0:
                print(f"回复成功: 评论ID {comment_id}")
                return True
            else:
                print(f"回复失败: {result.get('message')}")
                return False
        except Exception as e:
            print(f"请求失败: {e}")
            return False
    
    def batch_reply(self, comments, good_texts=None, mid_texts=None, bad_texts=None, reply_template=None):
        # 默认回复池
        default_good = [
            "感谢您的评价，我们会继续努力提供更好的服务！",
            "谢谢亲的好评，有您的鼓励我们会做的更好，期待您的再次光临！",
            "非常感谢您的支持与肯定，我们会一如既往地为您提供优质服务！",
            "感谢您的光临和评价，您的满意是我们最大的动力！",
            "谢谢您的评价，我们会继续努力提升服务质量，期待您的再次光临！"
        ]
        default_mid = [
            "感谢您的反馈，我们会持续改进，争取让您下次更满意！",
            "感谢您的中肯评价，我们会认真参考您的建议。",
            "谢谢您的建议，我们会努力提升服务体验。"
        ]
        default_bad = [
            "很抱歉没有让您满意，我们会加强改进，欢迎随时反馈问题。",
            "对不起给您带来不便，我们会认真整改，期待您的下次体验。",
            "感谢您的反馈，我们会查找原因，努力提升服务质量。"
        ]
        
        # 允许自定义池
        good_texts = good_texts or default_good
        mid_texts = mid_texts or default_mid
        bad_texts = bad_texts or default_bad
        
        success_count = 0
        for comment in comments:
            # comm_score: 1=好评, 2=中评, 3=差评
            score = int(comment.get('commScore', 0))
            if score == 1:
                pool = good_texts
            elif score == 2:
                pool = mid_texts
            elif score == 3:
                pool = bad_texts
            else:
                pool = good_texts  # 默认用好评池
            reply_text = random.choice(pool)
            wm_poi_id = comment.get("wmPoiId", None)
            if self.reply_comment(comment["id"], comment["createTime"], reply_text, wm_poi_id):
                success_count += 1
            time.sleep(random.uniform(2, 5))
        return success_count


def parse_cookies(cookies_str):
    cookie_dict = {}
    if not cookies_str:
        return cookie_dict
        
    for item in cookies_str.split(';'):
        if item.strip():
            try:
                key, value = item.strip().split('=', 1)
                cookie_dict[key.strip()] = value.strip()
            except ValueError:
                pass
    
    if "set_info" in cookie_dict:
        try:
            set_info_str = cookie_dict.get("set_info", "{}")
            region_id_match = re.search(r'"region_id"\s*:\s*"([^"]+)"', set_info_str)
            region_version_match = re.search(r'"region_version"\s*:\s*(\d+)', set_info_str)
            
            if region_id_match and "region_id" not in cookie_dict:
                cookie_dict["region_id"] = region_id_match.group(1)
            if region_version_match and "region_version" not in cookie_dict:
                cookie_dict["region_version"] = region_version_match.group(1)
        except:
            pass
            
    return cookie_dict

def auto_reply_comments(cookies, begin_time=None, end_time=None, comm_score=0, good_texts=None, mid_texts=None, bad_texts=None):
    """
    自动回复美团外卖评论
    
    参数:
        cookies: Cookie字典或字符串
        begin_time: 开始时间，时间戳或None（默认近一个月）
        end_time: 结束时间，时间戳或None（默认当前时间）
        comm_score: 评论类型，0代表所有未回复，1代表好评，2代表中评，3代表差评
        good_texts: 好评回复池（可选）
        mid_texts: 中评回复池（可选）
        bad_texts: 差评回复池（可选）
    返回:
        成功回复的数量
    """
    if isinstance(cookies, str):
        cookies = parse_cookies(cookies)
    
    crawler = MeituanCommentsCrawler(cookies=cookies)
    comments = crawler.crawl_all_comments(max_pages=10, begin_time=begin_time, end_time=end_time, comm_score=comm_score)
    # print(f"共爬取 {len(comments)} 条评论")  # 减少打印

    if not comments:
        # print("没有需要回复的评论")  # 减少打印
        return 0
    
    # 需要补充commScore字段
    for c in comments:
        if 'commScore' not in c:
            c['commScore'] = comm_score
    
    replier = MeituanCommentsReply(cookies=cookies)
    success_count = replier.batch_reply(comments, good_texts=good_texts, mid_texts=mid_texts, bad_texts=bad_texts)
    print(f"成功回复 {success_count} 条评论")
    return success_count

if __name__ == "__main__":
    example_cookie = "_lxsdk_cuid=19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8; uuid=7d87f1bd424c40c68858.1733153057.1.0.0; wm_order_channel=default; swim_line=default; utm_source=; utm_source_rg=; userId=; iuuid=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; _lxsdk=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; device_uuid=!ead860f5-29e5-4e03-811b-00a935f69de9; bizad_cityId=440100; bizad_second_city_id=440100; bizad_third_city_id=440106; wmPoiName=%E7%A6%BE%E6%B9%98%E5%AE%B4%C2%B7%E6%B9%96%E5%8D%97%E5%AE%B6%E5%B8%B8%E8%8F%9C%EF%BC%88%E6%A3%A0%E4%B8%8B%E5%BA%97%EF%BC%89; bizad_first_tag_id=1000; grayConsistency=false; _ga_95GX0SH5GM=GS2.1.s1751944327$o3$g0$t1751944327$j60$l0$h0; _ga=GA1.1.712315181.1733152796; _ga_FSX5S86483=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; _ga_LYVVHCWVNG=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; acctName=wmswlp1318940; token=00TeAY6NpXXm5ohmgn3DMzrLTRnfidsqWREOpnGF01N4*; bsid=toi1UXxirwcmNMjypFyU-2mMkLqwVV-qo__iZg-DJ17Ecwt2ay6xBq7352UO_b2AZ5t3urWRRn-ylLP7r6W2Ng; _lx_utm=utm_source%3Dbing%26utm_medium%3Dorganic; WEBDFPID=5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4-1752899097845-1733153579225QUUSWESfd79fef3d01d5e9aadc18ccd4d0c95075458; wmPoiId=27260079; acctId=229540584; _lxsdk_s=1981bc73503-c81-ed2-62b%7C%7C205"
    success_count = auto_reply_comments(
        cookies=example_cookie,
        comm_score=0,  # 0为全部，1/2/3为单独类型
        good_texts=["感谢您的好评！", "欢迎再次光临！"],
        mid_texts=["谢谢您的建议，我们会改进。"],
        bad_texts=["很抱歉没让您满意，我们会努力改进。"]
    )
    print(f"自动回复完成，共成功回复 {success_count} 条评论") 