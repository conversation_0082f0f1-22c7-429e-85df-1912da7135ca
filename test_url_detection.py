#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试URL检测逻辑
"""

def test_url_detection():
    """测试URL检测逻辑"""
    
    # 测试URL列表
    test_urls = [
        # 登录成功的URL
        "https://e.waimai.meituan.com/?time=*************&region_id=**********&region_version=**********",
        "https://waimaie.meituan.com/?time=*************&region_id=**********&region_version=**********",
        
        # 登录页面URL
        "https://e.waimai.meituan.com/new_fe/login_gw#/login",
        "https://passport.meituan.com/account/unitivelogin",
        
        # 其他页面URL
        "https://e.waimai.meituan.com/gw/static_resource/product",
        "https://waimaie.meituan.com/dashboard",
        "https://www.meituan.com/",
    ]
    
    print("🧪 测试URL检测逻辑...")
    print("=" * 80)
    
    for i, current_url in enumerate(test_urls, 1):
        print(f"\n[测试 {i}] URL: {current_url}")
        
        # 应用检测逻辑
        if ('e.waimai.meituan.com' in current_url or 'waimaie.meituan.com' in current_url) and 'time=' in current_url:
            print("✅ 检测结果: 登录成功！")
        elif 'login' in current_url:
            print("📝 检测结果: 仍在登录页面")
        else:
            print("🔍 检测结果: 页面URL未匹配登录成功条件")
    
    print("\n" + "=" * 80)
    print("🎉 URL检测逻辑测试完成！")
    print("\n📋 检测规则说明:")
    print("✅ 登录成功: 包含 'e.waimai.meituan.com' 或 'waimaie.meituan.com' 且包含 'time=' 参数")
    print("📝 登录页面: URL中包含 'login'")
    print("🔍 其他页面: 不匹配以上条件的页面")

if __name__ == "__main__":
    test_url_detection()
