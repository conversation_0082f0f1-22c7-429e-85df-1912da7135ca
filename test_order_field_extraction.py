#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试订单字段提取功能
"""

import re

def extract_order_field(log_message, field_name):
    """从日志消息中提取指定字段的值"""
    # 匹配字段名后面的值，支持中文冒号和英文冒号
    pattern = f"{field_name}[：:]\\s*([^\\n\\r]+)"
    match = re.search(pattern, log_message)
    return match.group(1).strip() if match else '-'

def format_meal_time(time_str):
    """格式化出餐用时"""
    if not time_str or time_str == '-':
        return '-'
    
    # 提取数字部分
    match = re.search(r'(\d+)', time_str)
    if not match:
        return time_str
    
    seconds = int(match.group(1))
    if seconds < 60:
        return f"{seconds}秒"
    else:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds}秒" if remaining_seconds > 0 else f"{minutes}分钟"

def test_field_extraction():
    """测试字段提取功能"""
    print("🧪 测试订单字段提取功能...")
    
    # 模拟日志消息
    sample_log = """自动出餐任务执行完成，成功出餐 2 个订单，耗时: 15.32秒

订单详情:
订单1:
  订单号: 3001705621215774830
  日序号: 35
  订单类型: 预订单
  下单时间: 2025-01-28 11:09:46
  出餐时间: 2025-01-28 11:25:18
  出餐用时: 932秒
  配送时间: 2025-01-28 12:30:00

订单2:
  订单号: 3001705621215774831
  日序号: 36
  订单类型: 普通订单
  下单时间: 2025-01-28 11:15:22
  出餐时间: 2025-01-28 11:25:18
  出餐用时: 596秒"""
    
    print("📋 原始日志消息:")
    print(sample_log)
    
    print("\n🔍 字段提取测试:")
    
    # 测试各个字段的提取
    fields_to_test = [
        '订单号',
        '日序号', 
        '订单类型',
        '下单时间',
        '出餐时间',
        '出餐用时'
    ]
    
    for field in fields_to_test:
        value = extract_order_field(sample_log, field)
        if field == '出餐用时':
            formatted_value = format_meal_time(value)
            print(f"  {field}: {value} -> {formatted_value}")
        else:
            print(f"  {field}: {value}")
    
    print("\n📊 表格显示效果预览:")
    print("┌─────────────────────┬────────┬──────────┬─────────────────────┬─────────────────────┬──────────┐")
    print("│ 订单号              │ 日序号 │ 订单类型 │ 下单时间            │ 出餐时间            │ 出餐用时 │")
    print("├─────────────────────┼────────┼──────────┼─────────────────────┼─────────────────────┼──────────┤")
    
    # 提取第一个订单的信息
    order_no = extract_order_field(sample_log, '订单号')
    day_seq = extract_order_field(sample_log, '日序号')
    order_type = extract_order_field(sample_log, '订单类型')
    order_time = extract_order_field(sample_log, '下单时间')
    complete_time = extract_order_field(sample_log, '出餐时间')
    meal_time = format_meal_time(extract_order_field(sample_log, '出餐用时'))
    
    print(f"│ {order_no:<19} │ {day_seq:<6} │ {order_type:<8} │ {order_time:<19} │ {complete_time:<19} │ {meal_time:<8} │")
    print("└─────────────────────┴────────┴──────────┴─────────────────────┴─────────────────────┴──────────┘")

def test_time_formatting():
    """测试时间格式化功能"""
    print("\n🧪 测试时间格式化功能...")
    
    test_cases = [
        "932秒",
        "596秒", 
        "45秒",
        "120秒",
        "3661秒",
        "60秒",
        "-",
        ""
    ]
    
    print("📋 时间格式化测试:")
    for case in test_cases:
        formatted = format_meal_time(case)
        print(f"  {case:<10} -> {formatted}")

if __name__ == "__main__":
    print("🚀 订单字段提取功能测试")
    print("=" * 60)
    
    # 测试字段提取
    test_field_extraction()
    
    # 测试时间格式化
    test_time_formatting()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("\n📋 功能说明:")
    print("✅ extractOrderField() - 从日志中提取指定字段值")
    print("✅ formatMealTime() - 格式化出餐用时显示")
    print("✅ 支持中文冒号和英文冒号的字段匹配")
    print("✅ 自动将秒数转换为分钟+秒数的友好显示")
    print("✅ 前端表格将显示6个独立的订单字段列")
