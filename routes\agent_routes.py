#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理管理路由
"""

from flask import Blueprint, request, jsonify
from utils.db_utils import get_connection
import hashlib

agent_bp = Blueprint('agent', __name__)

@agent_bp.route('/', methods=['GET'])
def get_agents():
    """获取代理列表"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT id, agent_account, agent_password, role, balance, created_at, updated_at
        FROM agents
        ORDER BY created_at DESC
        """
        cursor.execute(query)
        agents = cursor.fetchall()
        
        # 处理时间格式
        for agent in agents:
            if agent['created_at']:
                agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if agent['updated_at']:
                agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            # 转换balance为整数
            if agent['balance'] is not None:
                agent['balance'] = int(agent['balance'])
        
        return jsonify({
            'code': 0,
            'message': '获取成功',
            'data': agents
        })
    except Exception as e:
        print(f"获取代理列表失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'获取失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@agent_bp.route('/<int:agent_id>', methods=['GET'])
def get_agent(agent_id):
    """获取单个代理信息"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT id, agent_account, agent_password, role, balance, created_at, updated_at
        FROM agents WHERE id = %s
        """
        cursor.execute(query, [agent_id])
        agent = cursor.fetchone()
        
        if not agent:
            return jsonify({
                'code': 1,
                'message': '代理不存在',
                'data': None
            }), 404
        
        # 处理时间格式
        if agent['created_at']:
            agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if agent['updated_at']:
            agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        # 转换balance为整数
        if agent['balance'] is not None:
            agent['balance'] = int(agent['balance'])
        
        return jsonify({
            'code': 0,
            'message': '获取成功',
            'data': agent
        })
    except Exception as e:
        print(f"获取代理信息失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'获取失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@agent_bp.route('/', methods=['POST'])
def create_agent():
    """创建代理"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        
        # 检查必填字段
        required_fields = ['agent_account', 'agent_password']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'code': 1,
                    'message': f'缺少必填字段: {field}',
                    'data': None
                }), 400
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查代理账号是否已存在
        check_query = "SELECT id FROM agents WHERE agent_account = %s"
        cursor.execute(check_query, [data['agent_account']])
        existing_agent = cursor.fetchone()
        
        if existing_agent:
            return jsonify({
                'code': 1,
                'message': '代理账号已存在',
                'data': None
            }), 400
        
        # 密码加密
        password_hash = hashlib.md5(data['agent_password'].encode('utf-8')).hexdigest()
        
        # 创建新代理
        insert_query = """
        INSERT INTO agents (agent_account, agent_password, role, balance, created_at, updated_at)
        VALUES (%s, %s, %s, %s, NOW(), NOW())
        """
        
        cursor.execute(insert_query, [
            data['agent_account'],
            password_hash,
            data.get('role', 'user'),  # 默认角色为user
            data.get('balance', 0)
        ])
        
        connection.commit()
        
        # 获取创建的代理信息
        agent_id = cursor.lastrowid
        cursor.execute("SELECT * FROM agents WHERE id = %s", [agent_id])
        agent = cursor.fetchone()
        
        # 处理时间格式
        if agent['created_at']:
            agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if agent['updated_at']:
            agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        # 转换balance为整数
        if agent['balance'] is not None:
            agent['balance'] = int(agent['balance'])
        
        return jsonify({
            'code': 0,
            'message': '创建成功',
            'data': agent
        })
    except Exception as e:
        print(f"创建代理失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'创建失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@agent_bp.route('/<int:agent_id>', methods=['PUT'])
def update_agent(agent_id):
    """更新代理信息"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查代理是否存在
        check_query = "SELECT id FROM agents WHERE id = %s"
        cursor.execute(check_query, [agent_id])
        existing_agent = cursor.fetchone()
        
        if not existing_agent:
            return jsonify({
                'code': 1,
                'message': '代理不存在',
                'data': None
            }), 404
        
        # 构建更新查询
        update_fields = []
        update_values = []
        
        fields_to_update = ['balance']
        
        for field in fields_to_update:
            if field in data:
                update_fields.append(f"{field} = %s")
                update_values.append(data[field])
        
        # 处理账户名更新
        if 'agent_account' in data:
            # 检查新账户名是否已存在
            check_account_query = "SELECT id FROM agents WHERE agent_account = %s AND id != %s"
            cursor.execute(check_account_query, [data['agent_account'], agent_id])
            existing_account = cursor.fetchone()
            
            if existing_account:
                return jsonify({
                    'code': 1,
                    'message': '账户名已存在',
                    'data': None
                }), 400
            
            update_fields.append("agent_account = %s")
            update_values.append(data['agent_account'])
            
            # 账户名变化时，客户端需要重新登录
            return jsonify({
                'code': 0,
                'message': '账户名更新成功，客户端需要重新登录',
                'data': None
            })
        
        # 处理密码更新
        if 'agent_password' in data:
            password_hash = hashlib.md5(data['agent_password'].encode('utf-8')).hexdigest()
            update_fields.append("agent_password = %s")
            update_values.append(password_hash)
            
            # 密码变化时，客户端需要重新登录
            return jsonify({
                'code': 0,
                'message': '密码更新成功，客户端需要重新登录',
                'data': None
            })
        
        if not update_fields:
            return jsonify({
                'code': 1,
                'message': '没有提供有效的更新字段',
                'data': None
            }), 400
        
        # 添加更新时间
        update_fields.append("updated_at = NOW()")
        
        # 执行更新
        update_query = f"UPDATE agents SET {', '.join(update_fields)} WHERE id = %s"
        update_values.append(agent_id)
        
        cursor.execute(update_query, update_values)
        connection.commit()
        
        # 获取更新后的代理信息
        cursor.execute("SELECT * FROM agents WHERE id = %s", [agent_id])
        agent = cursor.fetchone()
        
        # 处理时间格式
        if agent['created_at']:
            agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if agent['updated_at']:
            agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        # 转换balance为整数
        if agent['balance'] is not None:
            agent['balance'] = int(agent['balance'])
        
        return jsonify({
            'code': 0,
            'message': '更新成功',
            'data': agent
        })
    except Exception as e:
        print(f"更新代理失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'更新失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@agent_bp.route('/<int:agent_id>', methods=['DELETE'])
def delete_agent(agent_id):
    """删除代理"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查代理是否存在
        check_query = "SELECT id FROM agents WHERE id = %s"
        cursor.execute(check_query, [agent_id])
        existing_agent = cursor.fetchone()
        
        if not existing_agent:
            return jsonify({
                'code': 1,
                'message': '代理不存在',
                'data': None
            }), 404
        
        # 执行删除
        delete_query = "DELETE FROM agents WHERE id = %s"
        cursor.execute(delete_query, [agent_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '删除成功',
            'data': None
        })
    except Exception as e:
        print(f"删除代理失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'删除失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 