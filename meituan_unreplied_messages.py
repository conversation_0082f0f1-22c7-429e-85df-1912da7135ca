import requests
import json
import time

def parse_cookies(cookies_str):
    cookie_dict = {}
    if not cookies_str:
        return cookie_dict
        
    for item in cookies_str.split(';'):
        if item.strip():
            try:
                key, value = item.strip().split('=', 1)
                cookie_dict[key.strip()] = value.strip()
            except ValueError:
                pass
    return cookie_dict

class MeituanUnrepliedMessages:
    def __init__(self, cookies=None):
        self.base_url = "https://e.waimai.meituan.com/gw/api/im/index/getChatInfosByType"
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "content-type": "application/x-www-form-urlencoded",
            "origin": "https://e.waimai.meituan.com",
            "referer": "https://e.waimai.meituan.com/new_fe/orderbusiness",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
        }
        
        if cookies is None:
            self.cookies = {
                "_lxsdk_cuid": "19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8",
                "token": "05W1Nmbe8ivGrsq-gJo2_7yn-FcouGnTADvO3WssVNso*",
                "wmPoiId": "27260079",
                "acctId": "126059518",
                "region_id": "1000420600",
                "region_version": "1743386677"
            }
        else:
            self.cookies = cookies
        
        self.headers["cookie"] = self._dict_to_cookie_string(self.cookies) if isinstance(self.cookies, dict) else self.cookies
        
        self.region_id = self.cookies.get("region_id", "1000420600")
        self.region_version = self.cookies.get("region_version", "1743386677")

    def _dict_to_cookie_string(self, cookie_dict):
        return "; ".join([f"{key}={value}" for key, value in cookie_dict.items()])

    def get_chat_infos(self):
        params = {
            "region_id": self.region_id,
            "region_version": self.region_version,
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.2.1",
            "mtgsig": json.dumps({
                "a1": "1.2",
                "a2": int(time.time() * 1000),
                "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
                "a5": "Yt+ssVqNnYa3lGzlckyuhI2M9QO0wMNyQDygkvyFNOC0XMdaZOD=",
                "a6": "hs1.639OPLqfM8F29yBqNRtVmqikuzZm+WUTpLzQ5dE2dKGQTzi4/ShzYogpUauBMsjv7yabdnhQCOUPf2cCZQKQ6ci5obKQgzDE2GTRB/TKjoVONQAOWocZdgDXZJkWUi+XR",
                "a8": "b9026a5d0b1e1fee400ecab0fe965a96",
                "a9": "3.2.1,7,1",
                "a10": "18",
                "x0": 4,
                "d1": "140a5ad4e563a8e42e92c117f169f200"
            })
        }

        data = {
            "type": json.dumps({"type": 2, "pageSize": 20})
        }

        try:
            response = requests.post(
                self.base_url,
                params=params,
                data=data,
                headers=self.headers
            )
            response.raise_for_status()
            result = response.json()
            return result
        except Exception:
            # print(f"请求失败: {e}")  # 减少打印，只在严重错误时打印
            return None

    def extract_chat_data(self, chat):
        return {
            "wmPoiId": chat.get("wmPoiId", 0),
            "wmPoiName": chat.get("wmPoiName", ""),
            "peerDxId": chat.get("peerDxId", 0),
            "chatId": chat.get("chatId", 0),
            "title": chat.get("title", ""),
            "icon": chat.get("icon", []),
            "poiLogoUrl": chat.get("poiLogoUrl", ""),
            "userId": chat.get("userId", 0),
            "lastMsg": chat.get("lastMsg", ""),  # 添加 lastMsg 字段
            "lastMsgTime": chat.get("lastMsgTime", 0)  # 添加最后消息时间
        }

    def get_unreplied_chats(self):
        result = self.get_chat_infos()
        if not result or result.get("code") != 0:
            # print("获取未回复消息失败")  # 减少打印
            return []

        chats = result.get("data", {}).get("chats", [])
        unreplied_chats = []

        for chat in chats:
            chat_data = self.extract_chat_data(chat)
            # 只保留userId有值的聊天（排除群聊）
            if chat_data.get('userId'):
                unreplied_chats.append(chat_data)

        return unreplied_chats

def get_unreplied_messages(cookies):
    """
    获取美团外卖未回复消息
    
    参数:
        cookies: Cookie字典或字符串
    
    返回:
        未回复消息列表
    """
    if isinstance(cookies, str):
        cookies = parse_cookies(cookies)
    
    chat_handler = MeituanUnrepliedMessages(cookies=cookies)
    unreplied_chats = chat_handler.get_unreplied_chats()

    if not unreplied_chats:
        return []
    
    return unreplied_chats

if __name__ == "__main__":
    example_cookie = "_lxsdk_cuid=19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8; uuid=7d87f1bd424c40c68858.1733153057.1.0.0; swim_line=default; utm_source_rg=; userId=; iuuid=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; device_uuid=!ead860f5-29e5-4e03-811b-00a935f69de9; uuid_update=true; _lxsdk=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; pushToken=0phE1U0H6Qkk_lD_dkMN_qQYm0SCZHc7UVkkP_TRhhHo*; _ga_95GX0SH5GM=GS2.1.s1751944327$o3$g0$t1751944327$j60$l0$h0; _ga=GA1.1.712315181.1733152796; _ga_FSX5S86483=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; _ga_LYVVHCWVNG=GS2.1.s1752041001$o1$g1$t1752041081$j41$l0$h0; WEBDFPID=5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4-1752899097845-1733153579225QUUSWESfd79fef3d01d5e9aadc18ccd4d0c95075458; wm_order_channel=sjzxpc; utm_source=60376; _lx_utm=utm_source%3D60376; au_trace_key_net=default; openh5_uuid=B1A7E74147190A15B44066EC63BB135A4778D1A27D40F03A0E45B3F951136545; isIframe=false; JSESSIONID=1iew72ec3xrlo1w75bkblx4sy1; acctId=229540584; token=0_n_-tXcTlprI0Mqdd74rqhEVbdGDIPFoqZHe97IjrpU*; wmPoiId=27260079; isOfflineSelfOpen=0; city_id=420600; isChain=0; ignore_set_router_proxy=false; region_id=1000420600; region_version=1743386677; bsid=Y1saq6a-0e5E6JkvPBVyMx3pjDA-5a1SwZKA9ZLOzujWv9dbpx3BjFFi8YweUmCr-gkMIY6_eRbdf8QS588ksw; city_location_id=420600; location_id=420606; has_not_waimai_poi=0; onlyForDaoDianAcct=0; cityId=410100; provinceId=410000; scIndex=0; set_info_single=%7B%22regionIdForSingle%22%3A%************%22%2C%22regionVersionForSingle%22%3A1743386677%7D; set_info=%7B%22wmPoiId%22%3A%2227260079%22%2C%22region_id%22%3A%************%22%2C%22region_version%22%3A1743386677%7D; wpush_server_url=wss://wpush.meituan.com; logan_session_token=7ubux5vvanvrjsykl9ii; setPrivacyTime=1_20250718; shopCategory=food; _lxsdk_s=1981cac806f-978-14c-564%7C229540584%7C873"
    unreplied_messages = get_unreplied_messages(cookies=example_cookie)
    print(f"未回复消息数据示例: {unreplied_messages[:3] if unreplied_messages else []}") 