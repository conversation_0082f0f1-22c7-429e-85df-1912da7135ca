import os
import time
import json
import random
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class MeituanLoginCDP:
    def __init__(self):
        self.driver = None
        self.target_url = "https://e.waimai.meituan.com/new_fe/login_gw#/login"

    def setup_driver(self):
        """设置Chrome驱动（使用本地chromedriver.exe）"""
        chrome_options = Options()
        # 取消自动化控制的提示
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        # 设置窗口大小，不最大化
        chrome_options.add_argument("--window-size=1200,800")
        # 禁用自动化特征
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        # 设置用户代理
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        chrome_options.add_argument(f"user-agent={user_agent}")
        # 其他浏览器设置
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--profile-directory=Default")
        chrome_options.add_argument("--disable-plugins-discovery")
        # 保持浏览器不自动关闭
        chrome_options.add_experimental_option("detach", True)
        
        # 使用项目目录下的drivers/chromedriver.exe
        driver_path = os.path.join(os.path.dirname(__file__), "drivers", "chromedriver.exe")
        if not os.path.exists(driver_path):
            raise FileNotFoundError(f"Chromedriver not found at: {driver_path}")

        service = Service(executable_path=driver_path)
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        # 进一步伪装
        if self.driver:
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        print("正在打开登录页面...")
        return self.driver

    def check_page_load(self):
        """检查页面是否正确加载"""
        if not self.driver:
            print("错误：浏览器未初始化")
            return False
        try:
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            page_title = self.driver.title
            current_url = self.driver.current_url
            print(f"页面标题: {page_title}")
            print(f"当前URL: {current_url}")

            # 放宽检查条件：只要页面有body元素就认为加载成功
            # 标题为空不影响功能，很多单页应用初始时标题就是空的
            if not page_title:
                print("警告: 页面标题为空，但页面已加载body元素，继续执行")
            return True
        except Exception as e:
            print(f"页面加载检查失败: {e}")
            return False

    def get_cookies(self):
        """获取当前页面的cookie"""
        if not self.driver:
            print("错误：浏览器未初始化")
            return []
        cookies = self.driver.get_cookies()
        print("获取到cookie:")
        for cookie in cookies:
            print(f"{cookie['name']}: {cookie['value']}")
        return cookies

    def get_shop_info(self, cookies):
        """获取店铺信息"""
        try:
            # 构建cookie字符串
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
            cookie_str = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
            
            # 获取acctId和region信息
            acct_id = cookie_dict.get('acctId', '')
            region_id = cookie_dict.get('region_id', '1000420600')
            region_version = cookie_dict.get('region_version', '1743386677')
            
            if not acct_id:
                print("错误：cookie中缺少acctId")
                return None
            
            # 请求店铺信息
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
                "Cookie": cookie_str,
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Referer": "https://e.waimai.meituan.com/new_fe/orderbusiness",
                "Origin": "https://e.waimai.meituan.com"
            }
            
            params = {
                "region_id": region_id,
                "region_version": region_version,
                "acctId": acct_id,
                "yodaReady": "h5",
                "csecplatform": "4",
                "csecversion": "3.2.1",
                "mtgsig": json.dumps({
                    "a1": "1.2",
                    "a2": int(time.time() * 1000),
                    "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
                    "a5": "XOE8Teh8fT2R0q/348/l89lo9fV2PMCykjNws7Z3n6qEBz3VFmeXpyw3ZCYG5uoWTc==",
                    "a6": "hs1.6gX5QvP3kL9C+7jPyUu16Kjp5iynl4ZirdlkeL38JQK2jEpjVIqzJi4uMYccDDdqOldT5zqPpXc207Gcbu6dv0CZhvpvq4BJ25W4au53UnFb/yMkA70AjrH9cD0yPKqLUzobP8m9OB9FKtr/g6uagmg==",
                    "a8": "f0364663eac0f26bc68b492e2e1eda21",
                    "a9": "3.2.1,7,69",
                    "a10": "2c",
                    "x0": 4,
                    "d1": "979a24e3631aafd931ae0b53f4808b38"
                })
            }
            
            url = "https://e.waimai.meituan.com/api/v2/account/homePage"
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('code') == 0:
                data = result.get('data', {})
                return {
                    'acctName': data.get('acctName', ''),
                    'wmPoiName': data.get('wmPoiName', ''),
                    'wmPoiId': data.get('wmPoiId', ''),
                    'regionId': data.get('regionId', ''),
                    'regionVersion': data.get('regionVersion', '')
                }
            else:
                print(f"获取店铺信息失败: {result.get('msg', '未知错误')}")
                return None
                
        except Exception as e:
            print(f"获取店铺信息出错: {e}")
            return None

    def get_shop_logo(self, cookies, wm_poi_id):
        """获取店铺logo"""
        try:
            # 构建cookie字符串
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
            cookie_str = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
            
            # 获取region信息
            region_id = cookie_dict.get('region_id', '1000420600')
            region_version = cookie_dict.get('region_version', '1743386677')
            
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
                "Cookie": cookie_str,
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Referer": "https://e.waimai.meituan.com/new_fe/orderbusiness",
                "Origin": "https://e.waimai.meituan.com"
            }
            
            params = {
                "wmPoiId": wm_poi_id,
                "region_id": region_id,
                "region_version": region_version,
                "yodaReady": "h5",
                "csecplatform": "4",
                "csecversion": "3.2.1",
                "mtgsig": json.dumps({
                    "a1": "1.2",
                    "a2": int(time.time() * 1000),
                    "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
                    "a5": "Riu23pQ4mAOBwKpS0iWwCRUMN868DwL/2AqAINOtaMkwTbUptW==",
                    "a6": "hs1.6yecw0RkuoBkdAQfJVFXHo7Ln0R5s+UL35hhj7LbItAkeJ9S+WFvsDxCArDjMP72u/WmC+N2pH9vIq+ao3IsOLi/mT2nmq9DWN9T6nLGGCk9JgaLZBGupzKTues4zfSDH",
                    "a8": "1a192451ced8acacdd74d499066dec94",
                    "a9": "3.2.1,7,153",
                    "a10": "52",
                    "x0": 4,
                    "d1": "f1a618ef3e91f6734f1ec11dcf37ee9b"
                })
            }
            
            url = "https://e.waimai.meituan.com/v2/chat/im/shop/logo"
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('code') == 0:
                logo_url = result.get('data', '')
                if logo_url.startswith('//'):
                    logo_url = 'https:' + logo_url
                return logo_url
            else:
                print(f"获取logo失败: {result.get('msg', '未知错误')}")
                return None
                
        except Exception as e:
            print(f"获取logo出错: {e}")
            return None

    def run(self):
        """运行登录数据捕获"""
        try:
            self.setup_driver()
            if self.driver:
                self.driver.get(self.target_url)
            else:
                print("错误：无法初始化浏览器")
                return None
            
            if not self.check_page_load():
                print("页面可能未正确加载，但继续尝试检测登录状态...")
                # 不要直接返回，继续执行登录检测
            
            print("请手动完成登录操作...")
            print("程序将每3秒检测一次页面URL变化...")

            # 每3秒检测一次页面URL，直到检测到登录成功的标志
            check_count = 0
            while True:
                try:
                    current_url = self.driver.current_url
                    check_count += 1
                    print(f"[检测 {check_count}] 当前页面URL: {current_url}")

                    # 检查当前页面URL是否包含登录成功后的特征
                    # 登录成功后会跳转到类似以下页面：
                    # https://e.waimai.meituan.com/?time=1753529675782&region_id=1000340400&region_version=1617770420
                    # https://waimaie.meituan.com/?time=1753526224899&region_id=1000440100&region_version=1651046402
                    if ('e.waimai.meituan.com' in current_url or 'waimaie.meituan.com' in current_url) and 'time=' in current_url:
                        print(f"\n✅ 检测到登录成功！当前页面: {current_url}")

                        # 获取cookie数据
                        cookies = self.driver.get_cookies()
                        cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}

                        # 验证必要的cookie是否存在
                        if 'acctId' in cookie_dict:
                            print("✅ 获取到完整的cookie数据:")
                            for cookie in cookies:
                                print(f"  {cookie['name']}: {cookie['value']}")
                            print("🎉 登录检测完成，开始获取店铺信息...")
                            break
                        else:
                            print("⚠️ 页面已跳转但cookie不完整，继续等待...")

                    # 检查是否还在登录页面
                    elif 'login' in current_url:
                        print("📝 仍在登录页面，请完成登录操作...")
                    else:
                        print(f"🔍 页面URL未匹配登录成功条件，继续监控...")

                except Exception as e:
                    print(f"❌ 检测URL时出错: {e}")

                time.sleep(3)

            # 获取店铺信息
            print("\n🏪 正在获取店铺信息...")
            shop_info = self.get_shop_info(cookies)
            if not shop_info:
                print("❌ 获取店铺信息失败")
                return None
            print(f"✅ 店铺信息获取成功: {shop_info.get('wmPoiName', '未知店铺')}")

            # 获取店铺logo
            print("\n🖼️ 正在获取店铺logo...")
            logo_url = self.get_shop_logo(cookies, cookie_dict.get('wmPoiId', ''))
            if logo_url:
                print(f"✅ Logo获取成功: {logo_url}")
            else:
                print("⚠️ Logo获取失败，但不影响主要功能")
            
            # 构建cookie字符串
            cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])

            # 构建返回数据
            result = {
                'cookie': cookie_str,
                'shop_id': cookie_dict.get('wmPoiId', ''),  # 店铺ID就是wmPoiId
                'login_account': shop_info.get('acctName', ''),
                'shop_name': shop_info.get('wmPoiName', ''),
                'logo': logo_url or '',
            }

            print(f"\n🎉 最终获取的数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            print("\n✅ 登录流程完成，准备关闭浏览器...")

            return result
            
        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
            return None
        finally:
            if self.driver:
                print("🔄 正在关闭浏览器...")
                try:
                    self.driver.quit()
                    print("✅ 浏览器已关闭")
                except Exception as e:
                    print(f"⚠️ 关闭浏览器时出错: {e}")

if __name__ == "__main__":
    login_cdp = MeituanLoginCDP()
    result = login_cdp.run()
    if result:
        print("成功获取到完整数据")
    else:
        print("未能获取到数据") 