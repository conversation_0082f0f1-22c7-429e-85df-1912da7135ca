#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
美团登录器GUI界面 - 简化版
"""

import sys
import json
import requests
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QTextEdit, QLabel, QProgressBar,
                             QMessageBox, QFrame, QLineEdit, QFormLayout, QDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

from meituan_login_cdp import MeituanLoginCDP

class LoginDialog(QDialog):
    """登录对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.agent_info = None
        self.init_ui()
        
    def init_ui(self):
        """初始化登录界面"""
        self.setWindowTitle("登录器登录")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("美团登录器")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setStyleSheet("color: #FF6B35; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("请输入代理账号和密码")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 20px;")
        layout.addWidget(subtitle_label)
        
        # 登录表单
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # 账号输入
        self.account_input = QLineEdit()
        self.account_input.setPlaceholderText("请输入代理账号")
        self.account_input.setFixedHeight(40)
        self.account_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #FF6B35;
            }
        """)
        form_layout.addRow("代理账号:", self.account_input)
        
        # 密码输入
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFixedHeight(40)
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #FF6B35;
            }
        """)
        form_layout.addRow("密码:", self.password_input)
        
        layout.addLayout(form_layout)
        
        # 登录按钮
        self.login_btn = QPushButton("登录")
        self.login_btn.setFixedHeight(45)
        self.login_btn.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.login_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF6B35, stop:1 #FF8E53);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #E55A2B, stop:1 #FF6B35);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #D44A1B, stop:1 #E55A2B);
            }
            QPushButton:disabled {
                background: #dee2e6;
                color: #6c757d;
            }
        """)
        self.login_btn.clicked.connect(self.handle_login)
        layout.addWidget(self.login_btn)
        
        # 状态显示
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("color: #dc3545; font-size: 12px;")
        layout.addWidget(self.status_label)
        
        # 回车登录
        self.account_input.returnPressed.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        
    def handle_login(self):
        """处理登录"""
        account = self.account_input.text().strip()
        password = self.password_input.text().strip()
        
        if not account or not password:
            self.status_label.setText("请输入账号和密码")
            return
            
        self.login_btn.setEnabled(False)
        self.login_btn.setText("登录中...")
        self.status_label.setText("正在验证账号...")
        
        try:
            # 调用后端登录接口
            response = requests.post(
                "http://localhost:5000/api/auth/login",
                json={
                    'agent_account': account,
                    'agent_password': password
                },
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            result = response.json()
            
            if result['code'] == 0:
                self.agent_info = result['data']['agent']
                self.status_label.setText("登录成功")
                self.status_label.setStyleSheet("color: #28a745; font-size: 12px;")
                QMessageBox.information(self, "登录成功", f"欢迎，{account}！")
                self.accept()
            else:
                self.status_label.setText(result['message'] or "登录失败")
                self.login_btn.setEnabled(True)
                self.login_btn.setText("登录")
                
        except Exception as e:
            self.status_label.setText(f"登录失败: {str(e)}")
            self.login_btn.setEnabled(True)
            self.login_btn.setText("登录")

class LoginThread(QThread):
    """登录线程"""
    progress_signal = pyqtSignal(str)
    result_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    
    def __init__(self, agent_info):
        super().__init__()
        self.agent_info = agent_info
    
    def run(self):
        try:
            self.progress_signal.emit("正在启动浏览器...")
            
            # 直接调用现有的登录脚本
            login_cdp = MeituanLoginCDP()
            
            self.progress_signal.emit("请在弹出的浏览器中完成登录...")
            result = login_cdp.run()
            
            if result:
                # 添加代理信息到结果中
                result['agent_id'] = self.agent_info['id']
                result['agent_account'] = self.agent_info['agent_account']
                
                self.progress_signal.emit("登录成功，正在上传数据...")
                self.result_signal.emit(result)
            else:
                self.error_signal.emit("登录失败，请重试")
                
        except Exception as e:
            self.error_signal.emit(f"登录过程出错: {str(e)}")

class UploadThread(QThread):
    """上传线程"""
    progress_signal = pyqtSignal(str)
    result_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    
    def __init__(self, data):
        super().__init__()
        self.data = data
    
    def run(self):
        try:
            self.progress_signal.emit("正在上传店铺信息到后端...")
            
            # 调用后端接口
            api_url = "http://localhost:5000/api/shops/init"
            
            response = requests.post(
                api_url,
                json=self.data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['code'] == 0:
                    self.progress_signal.emit("店铺信息上传成功！")
                    self.result_signal.emit(result)
                else:
                    self.error_signal.emit(f"上传失败: {result['message']}")
            else:
                self.error_signal.emit(f"请求失败，状态码: {response.status_code}")
                
        except Exception as e:
            self.error_signal.emit(f"上传过程出错: {str(e)}")

class MeituanLoginGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.login_thread = None
        self.upload_thread = None
        self.agent_info = None
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("美团外卖登录器")
        self.setGeometry(100, 100, 500, 400)
        
        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("美团外卖店铺登录器")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #FF6B35; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 代理信息显示
        self.agent_info_label = QLabel("未登录")
        self.agent_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.agent_info_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 20px;")
        main_layout.addWidget(self.agent_info_label)
        
        # 说明文字
        desc_label = QLabel("点击登录按钮，将弹出浏览器窗口，请在浏览器中完成美团外卖商家登录。")
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 20px;")
        main_layout.addWidget(desc_label)
        
        # 登录按钮
        self.login_btn = QPushButton("开始登录")
        self.login_btn.setFixedHeight(50)
        self.login_btn.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.login_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF6B35, stop:1 #FF8E53);
                color: white;
                border: none;
                border-radius: 25px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #E55A2B, stop:1 #FF6B35);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #D44A1B, stop:1 #E55A2B);
            }
            QPushButton:disabled {
                background: #dee2e6;
                color: #6c757d;
            }
        """)
        self.login_btn.clicked.connect(self.start_login)
        main_layout.addWidget(self.login_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                text-align: center;
                font-weight: bold;
                background: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF6B35, stop:1 #FF8E53);
                border-radius: 8px;
            }
        """)
        main_layout.addWidget(self.progress_bar)
        
        # 状态显示
        self.status_label = QLabel("等待登录...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("color: #666; font-size: 12px; margin: 10px 0;")
        main_layout.addWidget(self.status_label)
        
        # 日志显示区域
        log_frame = QFrame()
        log_frame.setFrameStyle(QFrame.Box)
        log_frame.setStyleSheet("border: 1px solid #CCCCCC; border-radius: 5px;")
        log_layout = QVBoxLayout(log_frame)
        
        log_title = QLabel("操作日志")
        log_title.setStyleSheet("font-weight: bold; color: #333; margin-bottom: 5px;")
        log_layout.addWidget(log_title)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: none;
                background-color: #F8F9FA;
                font-family: 'Consolas', 'Microsoft YaHei', monospace;
                font-size: 11px;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        main_layout.addWidget(log_frame)
        
        # 底部按钮区域
        bottom_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.setFixedHeight(35)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 17px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a6268;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_log)
        bottom_layout.addWidget(self.clear_btn)
        
        bottom_layout.addStretch()
        
        self.exit_btn = QPushButton("退出")
        self.exit_btn.setFixedHeight(35)
        self.exit_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 17px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        self.exit_btn.clicked.connect(self.close_application)
        bottom_layout.addWidget(self.exit_btn)
        
        main_layout.addLayout(bottom_layout)
        
        # 添加日志
        self.add_log("系统启动完成")
        self.add_log("请先登录代理账号")
        
    def start_login(self):
        """开始登录"""
        # 如果还没有登录代理账号，先显示登录对话框
        if not self.agent_info:
            login_dialog = LoginDialog(self)
            if login_dialog.exec_() == QDialog.Accepted:
                self.agent_info = login_dialog.agent_info
                if self.agent_info:
                    self.agent_info_label.setText(f"当前代理: {self.agent_info['agent_account']}")
                    self.add_log(f"代理登录成功: {self.agent_info['agent_account']}")
            else:
                return
        
        if self.login_thread and self.login_thread.isRunning():
            return
            
        self.login_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText("正在登录...")
        
        self.add_log("开始登录流程...")
        
        # 启动登录线程
        self.login_thread = LoginThread(self.agent_info)
        self.login_thread.progress_signal.connect(self.update_progress)
        self.login_thread.result_signal.connect(self.on_login_success)
        self.login_thread.error_signal.connect(self.on_login_error)
        self.login_thread.start()
        
    def update_progress(self, message):
        """更新进度信息"""
        self.status_label.setText(message)
        self.add_log(message)
        
    def on_login_success(self, result):
        """登录成功回调"""
        self.add_log("登录成功，获取到店铺信息")
        self.add_log(f"店铺名称: {result.get('shop_name', '未知')}")
        self.add_log(f"登录账号: {result.get('login_account', '未知')}")
        self.add_log(f"绑定代理: {result.get('agent_account', '未知')}")
        
        # 启动上传线程
        self.upload_thread = UploadThread(result)
        self.upload_thread.progress_signal.connect(self.update_progress)
        self.upload_thread.result_signal.connect(self.on_upload_success)
        self.upload_thread.error_signal.connect(self.on_upload_error)
        self.upload_thread.start()
        
    def on_login_error(self, error):
        """登录失败回调"""
        self.add_log(f"登录失败: {error}")
        self.status_label.setText("登录失败")
        self.progress_bar.setVisible(False)
        self.login_btn.setEnabled(True)
        
        QMessageBox.critical(self, "登录失败", f"登录过程中出现错误:\n{error}")
        
    def on_upload_success(self, result):
        """上传成功回调"""
        self.add_log("店铺信息上传成功！")
        self.add_log(f"店铺ID: {result['data']['shop_id']}")
        self.status_label.setText("上传成功")
        self.progress_bar.setVisible(False)
        self.login_btn.setEnabled(True)
        
        QMessageBox.information(self, "上传成功", "店铺信息已成功上传到后端！")
        
    def on_upload_error(self, error):
        """上传失败回调"""
        self.add_log(f"上传失败: {error}")
        self.status_label.setText("上传失败")
        self.progress_bar.setVisible(False)
        self.login_btn.setEnabled(True)
        
        QMessageBox.critical(self, "上传失败", f"上传过程中出现错误:\n{error}")
        
    def add_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.ensureCursorVisible()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空")
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.login_thread and self.login_thread.isRunning():
            reply = QMessageBox.question(self, "确认退出", 
                                       "登录过程正在进行中，确定要退出吗？",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                event.ignore()
                return
                
        event.accept()
        
    def close_application(self):
        """关闭应用程序"""
        self.close()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = MeituanLoginGUI()
    window.show()
    
    sys.exit(app.exec_()) 