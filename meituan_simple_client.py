#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
美团WebSocket客户端 - 简化版

直接连接服务器、发送认证信息和消息，无需命令行参数
预留了可变字段，方便修改
"""

import asyncio
import websockets.client
import websockets.exceptions
import time
import json
import random
import string
from meituan_serializer import serialize_auth, serialize_request, serialize_trans_up
from meituan_buffer import hex_dump, array_buffer_to_base64

def generate_random_uuid(length=9):
    """生成指定长度的随机UUID"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length))

# 预设的认证信息 - 可修改的字段
AUTH_DATA = {
    "version": "4.22.144",
    "passport": "27260079",  # 需要修改
    "password": "3_229540584_0_n_-tXcTlprI0Mqdd74rqhEVbdGDIPFoqZHe97IjrpU*",  # 需要修改
    "deviceid": "!ead860f5-29e5-4e03-811b-00a935f69ee9",  # 需要修改
    "os": 4,
    "sdkVersion": 1,
    "deviceType": 4,
    "pushToken": "",
    "deviceData": "",
    "supportMultiDevices": True,
    "traceId": 0,
    "swimlane": ""
}

# 预设的请求数据 - 可修改的字段
REQUEST_DATA = {
    "version": 1,
    "uri": 26869777,
    "deviceType": 4,
    "msgUuid": "xpWrqz5lu",
    "msgId": "",
    "senderUid": "3441622654",  # 需要修改
    "receiverUid": "2719047709",  # 需要修改
    "receiverAppId": 0,
    "pubUid": "138609546232",  # 需要修改
    "fromName": "三味凉皮·火鸡面烤冷面（酸辣粉，凉面）",  # 需要修改
    "cts": 0,  # 会在运行时更新为当前时间戳
    "pushType": 0,
    "direction": 1,
    "extension": "{\"order_id\":\"0\",\"c_name\":\"C**\",\"c_avatar_url\":\"https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/2c0a215905dbed8df38292ced36fdabe/im_icon_customer.png\",\"poi_logo_url\":\"http://p1.meituan.net/business/6627b7e1b960b25491607ab48634fcb583357.jpg\",\"user_id\":\"3770004248\",\"poi_name\":\"三味凉皮·火鸡面烤冷面（酸辣粉，凉面）\",\"main_uuid\":\"!ead860f5-29e5-4e03-811b-00a935f69de9\",\"im_role\":3,\"poi_id\":\"27260079\",\"poi_id_str\":\"DqduLlI9FGpjMB3Qd7pBpAI\",\"role_name\":\"三味凉皮·火鸡面烤冷面（酸辣粉，凉面）\",\"role_logo_url\":\"http://p1.meituan.net/business/6627b7e1b960b25491607ab48634fcb583357.jpg\",\"role_phone_number\":\"\",\"poi_nickname\":\"三味凉皮·火鸡面烤冷面（酸辣粉，凉面）客服\",\"tailNumber\":\"\",\"source\":\"PC\",\"role_type\":\"4\",\"deviceId\":\"!ead860f6-29e5-4e03-811b-00a935f69de9\",\"poi_order_count\":\"门店新客\"}",  # 需要修改
    "retries": 0,
    "toDeviceTypes": 0,
    "channel": 1001,
    "sessionSeqId": "",
    "compatible": "",
    "deviceId": "!ead860f5-29e5-4e03-811b-00a935f69de9",  # 需要修改 (与AUTH_DATA.deviceid相同)
    "messageReceivedType": 7,
    "type": 1,
    "message": {
        "version": 1,
        "text": "好的",  # 需要修改
        "font_name": "serif",
        "font_size": 12,
        "bold": False,
        "cipher_type": 0,
        "__type": 1
    }
}

# 预设的TransUp数据 - 可修改的字段
TRANSUP_DATA = {
    "svid": 410,
    "uid": "3441625931",  # 需要修改 (与REQUEST_DATA.senderUid相同)
    "flag": 0,
    "seqId": 0,
    "deviceId": "!ead860f5-29e5-4e03-811b-00a935f69de9",  # 需要修改 (与AUTH_DATA.deviceid相同)
    "traceId": 0,
    "swimlane": ""
}

def log(message):
    """简单的日志输出，格式与5.html一致"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

async def connect_and_send():
    """连接服务器并发送消息"""
    # 连接服务器
    log(f"正在连接到 wss://wmdxlwss.meituan.com...")
    try:
        websocket = await websockets.client.connect(
            "wss://wmdxlwss.meituan.com",
            ping_interval=30,
            ping_timeout=10,
            close_timeout=10,
            max_size=10 * 1024 * 1024,
            max_queue=32
        )
        log("WebSocket 连接已建立")
        
        # 启动消息接收任务
        receive_task = asyncio.create_task(receive_messages(websocket))
        
        # 发送认证信息
        log("正在发送认证信息...")
        auth_result = serialize_auth(AUTH_DATA)
        log(f"已发送 {auth_result['byteLength']} 字节的数据")
        await websocket.send(auth_result['bytes'])
        log("认证数据已发送")
        
        # 等待一小段时间，确保认证响应已收到
        await asyncio.sleep(0.2)
        
        # 更新请求中的时间戳
        REQUEST_DATA["cts"] = int(time.time() * 1000)
        
        # 发送TransUp消息
        log(f"正在发送TransUp消息... (msgUuid: {REQUEST_DATA['msgUuid']})")
        transup_result = serialize_trans_up(REQUEST_DATA, TRANSUP_DATA)
        log(f"已发送 {transup_result['byteLength']} 字节的数据")
        await websocket.send(transup_result['bytes'])
        log("TransUp 数据已发送")
        
        # 等待接收服务器响应
        await asyncio.sleep(0.2)
        
        # 关闭连接
        receive_task.cancel()
        try:
            await receive_task
        except asyncio.CancelledError:
            pass
        
        await websocket.close()
        log("WebSocket 连接已关闭")
        
    except Exception as e:
        log(f"错误: {e}")

async def receive_messages(websocket):
    """接收服务器消息"""
    try:
        while True:
            message = await websocket.recv()
            
            # 处理二进制消息
            if isinstance(message, bytes):
                log(f"收到二进制消息: {len(message)} 字节")
                
                # Base64输出
                base64_str = array_buffer_to_base64(message)
                log(f"Base64: {base64_str[:50]}...")
                
                # 十六进制输出
                hex_str = hex_dump(message).split('\n')[0]
                log(f"十六进制: {hex_str}")
            else:
                log(f"收到文本消息: {message}")
    except websockets.exceptions.ConnectionClosed:
        log("WebSocket 连接已关闭")
    except asyncio.CancelledError:
        # 任务被取消，正常退出
        pass
    except Exception as e:
        log(f"接收消息出错: {e}")

def send_message(passport=None, password=None, deviceid=None, 
                sender_uid=None, receiver_uid=None, pub_uid=None, 
                from_name=None, extension=None, device_id=None, 
                message_text=None, transup_uid=None, msg_uuid=None):
    """
    发送消息的主函数，可以自定义参数
    
    Args:
        passport: 认证信息中的passport
        password: 认证信息中的password
        deviceid: 认证信息中的deviceid
        sender_uid: 请求数据中的senderUid
        receiver_uid: 请求数据中的receiverUid
        pub_uid: 请求数据中的pubUid
        from_name: 请求数据中的fromName
        extension: 请求数据中的extension
        device_id: 请求数据中的deviceId和TransUp中的deviceId
        message_text: 消息内容
        transup_uid: TransUp数据中的uid
        msg_uuid: 消息的唯一标识符，如果不提供则自动生成
    """
    # 更新认证信息
    if passport:
        AUTH_DATA["passport"] = passport
    if password:
        AUTH_DATA["password"] = password
    if deviceid:
        AUTH_DATA["deviceid"] = deviceid
        # 同步更新请求和TransUp中的deviceId
        if not device_id:
            REQUEST_DATA["deviceId"] = deviceid
            TRANSUP_DATA["deviceId"] = deviceid
    
    # 更新请求数据
    if sender_uid:
        REQUEST_DATA["senderUid"] = sender_uid
        # 同步更新TransUp中的uid
        if not transup_uid:
            TRANSUP_DATA["uid"] = sender_uid
    if receiver_uid:
        REQUEST_DATA["receiverUid"] = receiver_uid
    if pub_uid:
        REQUEST_DATA["pubUid"] = pub_uid
    if from_name:
        REQUEST_DATA["fromName"] = from_name
    if extension:
        REQUEST_DATA["extension"] = extension
    if device_id:
        REQUEST_DATA["deviceId"] = device_id
        TRANSUP_DATA["deviceId"] = device_id
    if message_text:
        REQUEST_DATA["message"]["text"] = message_text
    
    # 生成或使用提供的消息UUID
    if msg_uuid:
        REQUEST_DATA["msgUuid"] = msg_uuid
    else:
        # 生成随机的9个字符作为msgUuid
        REQUEST_DATA["msgUuid"] = generate_random_uuid()
    
    # 更新TransUp数据
    if transup_uid:
        TRANSUP_DATA["uid"] = transup_uid
    
    # 运行异步函数
    asyncio.run(connect_and_send())

if __name__ == "__main__":
    # 使用默认参数发送消息
    send_message()
    
    # 示例：使用自定义参数发送消息
    # send_message(
    #     passport="你的passport",
    #     password="你的password",
    #     deviceid="你的deviceid",
    #     sender_uid="发送者ID",
    #     receiver_uid="接收者ID",
    #     pub_uid="发布者ID",
    #     from_name="发送者名称",
    #     message_text="你好，这是一条测试消息"
    # ) 
    
    # 示例：发送多条消息，每条消息都会自动生成不同的msgUuid
    # for i in range(3):
    #     send_message(message_text=f"这是第{i+1}条自动消息")
    #     time.sleep(1)  # 等待1秒再发送下一条 