#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试预订单优化功能
"""

from meituan_unprocessed_orders import MeituanUnprocessedOrders, auto_complete_meal
import time
from datetime import datetime

def test_preorder_reminder_time():
    """测试获取预订单提醒时间"""
    print("🧪 测试获取预订单提醒时间...")
    
    # 使用示例cookie
    example_cookie = {
        "region_id": "1000420100",
        "region_version": "1745222230",
        "token": "test_token",
        "wmPoiId": "27569158",
        "acctId": "231907397"
    }
    
    orders_handler = MeituanUnprocessedOrders(cookies=example_cookie)
    reminder_time = orders_handler.get_preorder_reminder_time()
    
    print(f"✅ 预订单提醒时间: {reminder_time}分钟")
    return reminder_time

def test_order_processing_logic():
    """测试订单处理逻辑"""
    print("\n🧪 测试订单处理逻辑...")
    
    # 模拟订单数据
    current_time = int(time.time())
    
    # 普通订单（确认时间为基准）
    normal_order = {
        "confirmTime": current_time - 400,  # 400秒前确认
        "deliveryBtime": 0,
        "wmOrderViewId": "TEST001",
        "wmPoiId": "12345"
    }
    
    # 预订单（配送时间为基准，需要减去提醒时间）
    preorder = {
        "confirmTime": current_time - 100,  # 100秒前确认
        "deliveryBtime": current_time + 1800,  # 30分钟后配送
        "wmOrderViewId": "TEST002", 
        "wmPoiId": "12345"
    }
    
    print("📋 订单数据分析:")
    print(f"当前时间: {datetime.fromtimestamp(current_time).strftime('%H:%M:%S')}")
    
    print(f"\n普通订单 {normal_order['wmOrderViewId']}:")
    print(f"  确认时间: {datetime.fromtimestamp(normal_order['confirmTime']).strftime('%H:%M:%S')}")
    print(f"  基准时间: 确认时间")
    print(f"  已过时间: {current_time - normal_order['confirmTime']}秒")
    
    print(f"\n预订单 {preorder['wmOrderViewId']}:")
    print(f"  确认时间: {datetime.fromtimestamp(preorder['confirmTime']).strftime('%H:%M:%S')}")
    print(f"  配送时间: {datetime.fromtimestamp(preorder['deliveryBtime']).strftime('%H:%M:%S')}")
    
    # 假设提醒时间为60分钟
    reminder_minutes = 60
    reminder_seconds = reminder_minutes * 60
    preorder_base_time = preorder['deliveryBtime'] - reminder_seconds
    
    print(f"  提醒时间: {reminder_minutes}分钟")
    print(f"  基准时间: {datetime.fromtimestamp(preorder_base_time).strftime('%H:%M:%S')} (配送时间 - 提醒时间)")
    print(f"  已过时间: {current_time - preorder_base_time}秒")

def test_complete_workflow():
    """测试完整工作流程"""
    print("\n🧪 测试完整工作流程...")
    
    # 注意：这里需要真实的cookie才能测试
    print("⚠️  需要真实的cookie才能进行完整测试")
    print("📝 工作流程说明:")
    print("1. 获取预订单提醒时间设置")
    print("2. 获取未出餐订单列表")
    print("3. 分析每个订单类型（普通订单 vs 预订单）")
    print("4. 计算基准时间：")
    print("   - 普通订单：使用确认时间")
    print("   - 预订单：使用配送时间 - 提醒时间")
    print("5. 判断是否需要出餐")
    print("6. 执行出餐操作")

if __name__ == "__main__":
    print("🚀 预订单优化功能测试")
    print("=" * 50)
    
    # 测试获取提醒时间（需要真实cookie）
    try:
        test_preorder_reminder_time()
    except Exception as e:
        print(f"❌ 获取提醒时间测试失败: {e}")
        print("💡 这通常是因为cookie无效或网络问题")
    
    # 测试处理逻辑
    test_order_processing_logic()
    
    # 测试完整工作流程
    test_complete_workflow()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n📋 优化要点总结:")
    print("✅ 自动获取商家设置的预订单提醒时间")
    print("✅ 预订单使用 配送时间-提醒时间 作为基准")
    print("✅ 普通订单使用确认时间作为基准")
    print("✅ 详细的日志输出，便于调试")
    print("✅ 支持订单类型识别和分类处理")
