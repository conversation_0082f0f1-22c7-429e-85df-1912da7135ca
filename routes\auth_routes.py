#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
认证管理路由
"""

from flask import Blueprint, request, jsonify, session
from utils.db_utils import get_connection
import hashlib
import time
import uuid

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    """代理登录"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        
        # 检查必填字段
        if not data or 'agent_account' not in data or 'agent_password' not in data:
            return jsonify({
                'code': 1,
                'message': '请输入账号和密码',
                'data': None
            }), 400
        
        agent_account = data['agent_account']
        agent_password = data['agent_password']

        # 密码加密（使用MD5）
        password_hash = hashlib.md5(agent_password.encode('utf-8')).hexdigest()

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 查询代理信息
        query = """
        SELECT id, agent_account, agent_password, role, balance, created_at, updated_at
        FROM agents
        WHERE agent_account = %s AND agent_password = %s
        """
        cursor.execute(query, [agent_account, password_hash])
        agent = cursor.fetchone()

        if not agent:
            return jsonify({
                'code': 1,
                'message': '账号或密码错误',
                'data': None
            }), 401
        
        # 处理时间格式
        if agent['created_at']:
            agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if agent['updated_at']:
            agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        # 转换balance为整数
        if agent['balance'] is not None:
            agent['balance'] = int(agent['balance'])
        
        # 生成更安全的token
        token_data = f"{agent['id']}_{agent['agent_account']}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        token = hashlib.sha256(token_data.encode('utf-8')).hexdigest()

        # 设置session，延长过期时间到30天
        session.permanent = True
        session['agent_id'] = agent['id']
        session['agent_account'] = agent['agent_account']
        session['login_time'] = int(time.time())
        session['token'] = token

        # 在返回的agent信息中包含原始密码（用于后续验证）
        agent['agent_password'] = agent_password

        return jsonify({
            'code': 0,
            'message': '登录成功',
            'data': {
                'agent': agent,
                'token': token,
                'expires_in': 2592000  # 30天过期时间（秒）
            }
        })
        
    except Exception as e:
        print(f"登录失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'登录失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """退出登录"""
    try:
        # 清除session
        session.clear()
        return jsonify({
            'code': 0,
            'message': '退出成功',
            'data': None
        })
    except Exception as e:
        print(f"退出失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'退出失败: {str(e)}',
            'data': None
        }), 500

@auth_bp.route('/profile', methods=['GET'])
def get_profile():
    """获取当前登录用户信息"""
    connection = None
    cursor = None
    try:
        # 检查是否已登录
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({
                'code': 1,
                'message': '未登录',
                'data': None
            }), 401
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询代理信息
        query = """
        SELECT id, agent_account, agent_password, role, balance, created_at, updated_at
        FROM agents WHERE id = %s
        """
        cursor.execute(query, [agent_id])
        agent = cursor.fetchone()
        
        if not agent:
            return jsonify({
                'code': 1,
                'message': '用户不存在',
                'data': None
            }), 404
        
        # 处理时间格式
        if agent['created_at']:
            agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if agent['updated_at']:
            agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        # 转换balance为整数
        if agent['balance'] is not None:
            agent['balance'] = int(agent['balance'])
        
        return jsonify({
            'code': 0,
            'message': '获取成功',
            'data': agent
        })
        
    except Exception as e:
        print(f"获取用户信息失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'获取失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@auth_bp.route('/update-password', methods=['PUT'])
def update_password():
    """修改密码"""
    connection = None
    cursor = None
    try:
        # 检查是否已登录
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({
                'code': 1,
                'message': '未登录',
                'data': None
            }), 401
        
        data = request.get_json()
        
        # 检查必填字段
        if not data or 'old_password' not in data or 'new_password' not in data:
            return jsonify({
                'code': 1,
                'message': '请输入旧密码和新密码',
                'data': None
            }), 400
        
        old_password = data['old_password']
        new_password = data['new_password']
        
        # 密码加密
        old_password_hash = hashlib.md5(old_password.encode('utf-8')).hexdigest()
        new_password_hash = hashlib.md5(new_password.encode('utf-8')).hexdigest()
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 验证旧密码
        check_query = "SELECT id FROM agents WHERE id = %s AND agent_password = %s"
        cursor.execute(check_query, [agent_id, old_password_hash])
        agent = cursor.fetchone()
        
        if not agent:
            return jsonify({
                'code': 1,
                'message': '旧密码错误',
                'data': None
            }), 400
        
        # 更新密码
        update_query = "UPDATE agents SET agent_password = %s, updated_at = NOW() WHERE id = %s"
        cursor.execute(update_query, [new_password_hash, agent_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '密码修改成功',
            'data': None
        })
        
    except Exception as e:
        print(f"修改密码失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'修改失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@auth_bp.route('/verify', methods=['POST'])
def verify_token():
    """验证token有效性"""
    connection = None
    cursor = None
    try:
        data = request.get_json()

        # 检查必填字段
        if not data or 'agent_account' not in data or 'agent_password' not in data:
            return jsonify({
                'code': 1,
                'message': '请输入账号和密码',
                'data': None
            }), 400

        agent_account = data['agent_account']
        agent_password = data['agent_password']

        # 密码加密（使用MD5）
        password_hash = hashlib.md5(agent_password.encode('utf-8')).hexdigest()

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 查询代理信息
        query = """
        SELECT id, agent_account, agent_password, role, balance, created_at, updated_at
        FROM agents
        WHERE agent_account = %s AND agent_password = %s
        """
        cursor.execute(query, [agent_account, password_hash])
        agent = cursor.fetchone()

        if not agent:
            return jsonify({
                'code': 1,
                'message': '账号或密码错误',
                'data': None
            }), 401

        # 处理时间格式
        if agent['created_at']:
            agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if agent['updated_at']:
            agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        # 转换balance为整数
        if agent['balance'] is not None:
            agent['balance'] = int(agent['balance'])

        # 在返回的agent信息中包含原始密码（用于后续验证）
        agent['agent_password'] = agent_password

        return jsonify({
            'code': 0,
            'message': '验证成功',
            'data': {
                'agent': agent,
                'valid': True
            }
        })

    except Exception as e:
        print(f"验证失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'验证失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()