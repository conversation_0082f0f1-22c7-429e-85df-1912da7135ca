#!/usr/bin/env python
# -*- coding: utf-8 -*-

import struct
import base64

class ByteBuffer:
    """
    ByteBuffer类，用于处理二进制数据，模拟JavaScript中的ByteBuffer实现
    """
    
    def __init__(self, capacity_or_buffer=1024, little_endian=False):
        """
        初始化ByteBuffer
        
        Args:
            capacity_or_buffer: 容量大小或已有的buffer
            little_endian: 是否使用小端序
        """
        self.little_endian = little_endian
        self.endian_mark = '<' if little_endian else '>'
        
        if isinstance(capacity_or_buffer, int):
            self.capacity = capacity_or_buffer
            self.buffer = bytearray(self.capacity)
        elif isinstance(capacity_or_buffer, (bytes, bytearray)):
            self.capacity = len(capacity_or_buffer)
            self.buffer = bytearray(capacity_or_buffer)
        else:
            raise TypeError("capacity_or_buffer必须是整数或bytes/bytearray")
            
        self.write_offset = 0
        self.read_offset = 0
        self.snapshot = None
    
    @staticmethod
    def wrap(buffer):
        """
        包装已有的buffer
        """
        if isinstance(buffer, (bytes, bytearray)):
            # 创建一个新的ByteBuffer并复制数据
            bb = ByteBuffer(len(buffer))
            bb.put_bytes(buffer)
            return bb
        elif isinstance(buffer, ByteBuffer):
            return buffer
        elif isinstance(buffer, list):
            # 创建一个新的ByteBuffer并复制数据
            data = bytearray(buffer)
            bb = ByteBuffer(len(data))
            bb.put_bytes(data)
            return bb
        else:
            return ByteBuffer(buffer)
    
    @staticmethod
    def str_to_bytes(string):
        """
        将字符串转换为字节数组
        
        Args:
            string: 要转换的字符串
            
        Returns:
            bytes: 转换后的字节数组
        """
        if string is None or string == '':
            return bytes()
        
        byte_buffer = ByteBuffer(len(string) * 3)
        byte_buffer.write_utf8_string(string)
        return byte_buffer.get_bytes()
    
    def resize(self, capacity):
        """
        调整buffer大小
        
        Args:
            capacity: 新的容量大小
            
        Returns:
            ByteBuffer: 调整大小后的ByteBuffer对象
        """
        if len(self.buffer) < capacity:
            new_buffer = bytearray(capacity)
            new_buffer[:len(self.buffer)] = self.buffer
            self.buffer = new_buffer
            self.capacity = capacity
        return self
    
    def position(self, offset=None):
        """
        获取或设置写入位置
        
        Args:
            offset: 要设置的位置，如果为None则返回当前位置
            
        Returns:
            int或ByteBuffer: 当前位置或设置位置后的ByteBuffer对象
        """
        if offset is not None:
            self.write_offset = offset
            return self
        else:
            return self.write_offset
    
    def get_bytes(self):
        """
        获取当前写入的字节数组
        
        Returns:
            bytes: 当前写入的字节数组
        """
        self.snapshot = bytes(self.buffer[:self.write_offset])
        return self.snapshot
    
    def get_array(self):
        """
        获取当前写入的字节数组
        
        Returns:
            bytearray: 当前写入的字节数组
        """
        return self.buffer[:self.write_offset]
    
    def write_byte(self, value):
        """
        写入一个字节
        
        Args:
            value: 要写入的字节值
            
        Returns:
            ByteBuffer: 写入后的ByteBuffer对象
        """
        if self.write_offset + 1 > self.capacity:
            self.capacity *= 2
            self.resize(max(self.capacity, self.write_offset + 1))
        
        self.buffer[self.write_offset] = value & 0xFF
        self.write_offset += 1
        return self
    
    def read_byte(self):
        """
        读取一个字节
        
        Returns:
            int: 读取的字节值
        """
        value = self.buffer[self.read_offset]
        self.read_offset += 1
        return value
    
    def put_int(self, value, offset=None):
        """
        写入一个整数
        
        Args:
            value: 要写入的整数值
            offset: 写入位置，如果为None则使用当前写入位置
            
        Returns:
            ByteBuffer: 写入后的ByteBuffer对象
        """
        relative = offset is None
        if relative:
            offset = self.write_offset
        
        offset_end = offset + 4
        if offset_end > self.capacity:
            self.resize(max(self.capacity * 2, offset_end))
        
        struct.pack_into(f"{self.endian_mark}I", self.buffer, offset, value)
        
        if relative:
            self.write_offset += 4
        
        return self
    
    def pop_int(self):
        """
        读取一个整数
        
        Returns:
            int: 读取的整数值
        """
        value = struct.unpack_from(f"{self.endian_mark}I", self.buffer, self.read_offset)[0]
        self.read_offset += 4
        return value
    
    def put_short(self, value, offset=None):
        """
        写入一个短整数
        
        Args:
            value: 要写入的短整数值
            offset: 写入位置，如果为None则使用当前写入位置
            
        Returns:
            ByteBuffer: 写入后的ByteBuffer对象
        """
        relative = offset is None
        if relative:
            offset = self.write_offset
        
        offset_end = offset + 2
        if offset_end > self.capacity:
            self.resize(max(self.capacity * 2, offset_end))
        
        struct.pack_into(f"{self.endian_mark}H", self.buffer, offset, value)
        
        if relative:
            self.write_offset += 2
        
        return self
    
    def pop_short(self):
        """
        读取一个短整数
        
        Returns:
            int: 读取的短整数值
        """
        value = struct.unpack_from(f"{self.endian_mark}H", self.buffer, self.read_offset)[0]
        self.read_offset += 2
        return value
    
    def put_long(self, value):
        """
        写入一个长整数
        
        Args:
            value: 要写入的长整数值
            
        Returns:
            ByteBuffer: 写入后的ByteBuffer对象
        """
        if isinstance(value, int):
            # 确保缓冲区足够大
            if self.write_offset + 8 > self.capacity:
                self.resize(max(self.capacity * 2, self.write_offset + 8))
            
            struct.pack_into(f"{self.endian_mark}Q", self.buffer, self.write_offset, value)
            self.write_offset += 8
        elif isinstance(value, str):
            # 尝试将字符串转换为数值
            try:
                num_value = int(value)
                return self.put_long(num_value)
            except:
                # 如果无法转换，写入0
                return self.put_long(0)
        elif value is None:
            # 写入0
            return self.put_long(0)
        else:
            # 默认写入0
            return self.put_long(0)
        
        return self
    
    def write_utf8_string(self, string, offset=None):
        """
        写入UTF-8编码的字符串
        
        Args:
            string: 要写入的字符串
            offset: 写入位置，如果为None则使用当前写入位置
            
        Returns:
            int: 写入的字节长度
        """
        relative = offset is None
        if relative:
            offset = self.write_offset
        
        # 计算UTF-8编码后的字节长度
        bytes_data = string.encode('utf-8')
        length = len(bytes_data)
        
        # 确保缓冲区足够大
        if offset + length > self.capacity:
            self.resize(max(self.capacity * 2, offset + length))
        
        # 写入UTF-8编码的字符串
        self.buffer[offset:offset+length] = bytes_data
        
        if relative:
            self.write_offset += length
        
        return length
    
    def read_utf8_string(self, length, offset=None):
        """
        读取UTF-8编码的字符串
        
        Args:
            length: 要读取的字节长度
            offset: 读取位置，如果为None则使用当前读取位置
            
        Returns:
            str: 读取的字符串
        """
        relative = offset is None
        if relative:
            offset = self.read_offset
        
        bytes_data = self.buffer[offset:offset+length]
        string = bytes_data.decode('utf-8')
        
        if relative:
            self.read_offset += length
        
        return string
    
    def put_bytes(self, bytes_data, offset=None):
        """
        写入字节数组
        
        Args:
            bytes_data: 要写入的字节数组
            offset: 写入位置，如果为None则使用当前写入位置
            
        Returns:
            ByteBuffer: 写入后的ByteBuffer对象
        """
        relative = offset is None
        if relative:
            offset = self.write_offset
        
        if not bytes_data or len(bytes_data) == 0:
            return self
        
        length = len(bytes_data)
        
        # 确保缓冲区足够大
        if offset + length > self.capacity:
            self.resize(max(self.capacity * 2, offset + length))
        
        # 写入字节数组
        self.buffer[offset:offset+length] = bytes_data
        
        if relative:
            self.write_offset += length
        
        return self

# 辅助函数
def array_buffer_to_base64(buffer):
    """
    将字节数组转换为Base64编码
    
    Args:
        buffer: 要转换的字节数组
        
    Returns:
        str: Base64编码的字符串
    """
    return base64.b64encode(buffer).decode('utf-8')

def base64_to_array_buffer(base64_str):
    """
    将Base64编码转换为字节数组
    
    Args:
        base64_str: Base64编码的字符串
        
    Returns:
        bytes: 转换后的字节数组
    """
    return base64.b64decode(base64_str)

def hex_dump(buffer, bytes_per_line=16):
    """
    打印二进制数据的十六进制表示
    
    Args:
        buffer: 要打印的字节数组
        bytes_per_line: 每行显示的字节数
        
    Returns:
        str: 格式化的十六进制表示
    """
    if isinstance(buffer, ByteBuffer):
        bytes_data = buffer.get_bytes()
    else:
        bytes_data = buffer
        
    result = ''
    
    for i in range(0, len(bytes_data), bytes_per_line):
        hex_part = f"{i:04x}: "
        ascii_part = ''
        
        for j in range(bytes_per_line):
            if i + j < len(bytes_data):
                byte = bytes_data[i + j]
                hex_part += f"{byte:02x} "
                ascii_part += chr(byte) if 32 <= byte <= 126 else '.'
            else:
                hex_part += "   "
        
        result += f"{hex_part} | {ascii_part}\n"
    
    return result

def array_buffer_to_hex_string(buffer):
    """
    将字节数组转换为格式化的十六进制字符串
    
    Args:
        buffer: 要转换的字节数组
        
    Returns:
        str: 格式化的十六进制字符串
    """
    if isinstance(buffer, ByteBuffer):
        bytes_data = buffer.get_bytes()
    else:
        bytes_data = buffer
        
    return ' '.join([f"{byte:02x}" for byte in bytes_data]) 