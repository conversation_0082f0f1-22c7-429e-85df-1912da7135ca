<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 代理信息
const agentInfo = ref({
  id: '',
  agent_account: '',
  balance: 0,
  created_at: '',
  updated_at: '',
})

// 修改密码表单
const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: '',
})

// 修改账户名表单
const accountForm = reactive({
  new_account: '',
})

// 账户名表单规则
const accountRules = {
  new_account: [
    { required: true, message: '请输入新的账户名', trigger: 'blur' },
    { min: 3, message: '账户名长度不能少于3位', trigger: 'blur' },
  ],
}

// 表单规则
const passwordRules = {
  old_password: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

const passwordFormRef = ref()
const accountFormRef = ref()
const loading = ref(false)
const passwordDialogVisible = ref(false)
const accountDialogVisible = ref(false)

// 获取代理信息
const getAgentInfo = async () => {
  try {
    const response = await fetch('/api/auth/profile', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const result = await response.json()

    if (result.code === 0) {
      agentInfo.value = result.data
    } else {
      ElMessage.error(result.message || '获取信息失败')
    }
  } catch (error) {
    console.error('获取代理信息错误:', error)
    ElMessage.error('获取信息失败')
  }
}

// 修改密码
const handleUpdatePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    loading.value = true

    const response = await fetch('/api/auth/update-password', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        old_password: passwordForm.old_password,
        new_password: passwordForm.new_password,
      }),
    })

    const result = await response.json()

    if (result.code === 0) {
      ElMessage.success('密码修改成功')
      passwordDialogVisible.value = false
      // 重置表单
      passwordForm.old_password = ''
      passwordForm.new_password = ''
      passwordForm.confirm_password = ''
    } else {
      ElMessage.error(result.message || '密码修改失败')
    }
  } catch (error) {
    console.error('修改密码错误:', error)
    ElMessage.error('密码修改失败')
  } finally {
    loading.value = false
  }
}

// 打开修改密码对话框
const openPasswordDialog = () => {
  passwordDialogVisible.value = true
}

// 关闭修改密码对话框
const closePasswordDialog = () => {
  passwordDialogVisible.value = false
  // 重置表单
  passwordForm.old_password = ''
  passwordForm.new_password = ''
  passwordForm.confirm_password = ''
}

// 修改账户名
const handleUpdateAccount = async () => {
  if (!accountFormRef.value) return

  try {
    await accountFormRef.value.validate()
    loading.value = true

    const response = await fetch(`/api/agents/${agentInfo.value.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        agent_account: accountForm.new_account,
      }),
    })

    const result = await response.json()

    if (result.code === 0) {
      ElMessage.success('账户名修改成功，请重新登录')
      // 清除本地存储
      localStorage.removeItem('userInfo')
      localStorage.removeItem('token')
      // 跳转到登录页
      window.location.href = '/login'
    } else {
      ElMessage.error(result.message || '账户名修改失败')
    }
  } catch (error) {
    console.error('修改账户名错误:', error)
    ElMessage.error('账户名修改失败')
  } finally {
    loading.value = false
  }
}

// 打开修改账户名对话框
const openAccountDialog = () => {
  accountForm.new_account = agentInfo.value.agent_account
  accountDialogVisible.value = true
}

// 关闭修改账户名对话框
const closeAccountDialog = () => {
  accountDialogVisible.value = false
  // 重置表单
  accountForm.new_account = ''
}

onMounted(() => {
  getAgentInfo()
})
</script>

<template>
  <div class="account-container">
    <el-card class="page-header-card">
      <div class="page-header">
        <div class="header-title">
          <el-icon class="header-icon"><User /></el-icon>
          <h2>账号管理</h2>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="getAgentInfo">
            <el-icon><RefreshRight /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card class="content-card">
      <div class="profile-section">
        <div class="profile-header">
          <el-avatar :size="80" class="profile-avatar">
            {{ agentInfo.agent_account?.charAt(0)?.toUpperCase() }}
          </el-avatar>
          <div class="profile-info">
            <h3 class="profile-name">{{ agentInfo.agent_account }}</h3>
            <p class="profile-role">代理账号</p>
          </div>
        </div>

        <el-divider />

        <div class="info-section">
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">代理ID：</label>
                <span class="info-value">{{ agentInfo.id }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">代理账号：</label>
                <span class="info-value">{{ agentInfo.agent_account }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">积分余额：</label>
                <span class="info-value balance">{{ agentInfo.balance || 0 }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">创建时间：</label>
                <span class="info-value">{{ agentInfo.created_at }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">更新时间：</label>
                <span class="info-value">{{ agentInfo.updated_at }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <el-divider />

        <div class="action-section">
          <el-button type="primary" @click="openAccountDialog">
            <el-icon><Edit /></el-icon>
            修改账户名
          </el-button>
          <el-button type="warning" @click="openPasswordDialog">
            <el-icon><Key /></el-icon>
            修改密码
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="500px"
      :before-close="closePasswordDialog"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="旧密码" prop="old_password">
          <el-input
            v-model="passwordForm.old_password"
            type="password"
            placeholder="请输入旧密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordForm.new_password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirm_password">
          <el-input
            v-model="passwordForm.confirm_password"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closePasswordDialog">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleUpdatePassword">
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改账户名对话框 -->
    <el-dialog
      v-model="accountDialogVisible"
      title="修改账户名"
      width="500px"
      :before-close="closeAccountDialog"
    >
      <el-form ref="accountFormRef" :model="accountForm" :rules="accountRules" label-width="100px">
        <el-form-item label="新账户名" prop="new_account">
          <el-input v-model="accountForm.new_account" placeholder="请输入新的账户名" clearable />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeAccountDialog">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleUpdateAccount">
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.account-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  margin: 0;
  padding: 0;
}

.page-header-card {
  margin-bottom: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 22px;
  margin-right: 8px;
  color: #1890ff;
}

.header-title h2 {
  font-size: 18px;
  color: #333;
  margin: 0;
  font-weight: 600;
}

.content-card {
  margin-bottom: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.profile-section {
  padding: 20px 0;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.profile-avatar {
  background: linear-gradient(135deg, #1890ff, #52c41a);
  color: white;
  font-weight: bold;
  font-size: 24px;
  margin-right: 20px;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.profile-role {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.info-section {
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.info-label {
  font-weight: bold;
  color: #333;
  min-width: 100px;
  margin-right: 16px;
}

.info-value {
  color: #666;
  flex: 1;
}

.info-value.balance {
  color: #1890ff;
  font-weight: bold;
  font-size: 20px;
}

.action-section {
  display: flex;
  justify-content: center;
  gap: 16px;
}

:deep(.el-button .el-icon) {
  margin-right: 4px;
}

:deep(.el-card__body) {
  padding: 24px;
}

:deep(.el-divider) {
  margin: 24px 0;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
