import requests
import json
import time
import re
import urllib.parse
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class MeituanChatInfo:
    def __init__(self, cookies=None):
        self.base_url_all = "https://e.waimai.meituan.com/gw/api/im/index/getAllChatInfos"
        self.base_url_by_type = "https://e.waimai.meituan.com/gw/api/im/index/getChatInfosByType"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Referer": "https://e.waimai.meituan.com/new_fe/imChat_gw",
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Host": "e.waimai.meituan.com",
            "Origin": "https://e.waimai.meituan.com",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "X-Requested-With": "XMLHttpRequest"
        }
        
        # 如果没有提供cookies，使用默认值
        if cookies is None:
            self.cookies = {
                "_lxsdk_cuid": "19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8",
                "token": "06JKjVDdvx48fVY266Q07U9uBYFMIAQI4j19FmCKU0GI*",
                "wmPoiId": "27260079",
                "acctId": "229540584",
                "region_id": "1000420600",
                "region_version": "1743386677"
            }
        else:
            self.cookies = cookies
        
        # 从cookie中提取必要的信息
        self.token = self.cookies.get("token")
        self.acct_id = self.cookies.get("acctId")
        self.wm_poi_id = self.cookies.get("wmPoiId")
        
        # 提取region_id和region_version
        self.region_id = self.cookies.get("region_id")
        self.region_version = self.cookies.get("region_version")
        
        # 如果cookie中没有直接的region_id和region_version，尝试从set_info中提取
        if (not self.region_id or not self.region_version) and "set_info" in self.cookies:
            try:
                set_info = json.loads(self.cookies.get("set_info", "{}"))
                self.region_id = self.region_id or set_info.get("region_id")
                self.region_version = self.region_version or set_info.get("region_version")
            except:
                pass
        
        # 确保必要的信息存在
        if not self.token or not self.acct_id or not self.wm_poi_id:
            raise ValueError("Cookie中缺少必要的信息: token, acctId 或 wmPoiId")
        
        if not self.region_id or not self.region_version:
            raise ValueError("Cookie中缺少必要的信息: region_id 或 region_version")
        
        # 打印提取的关键参数
        print(f"初始化参数: token={self.token}, acctId={self.acct_id}, wmPoiId={self.wm_poi_id}")
        print(f"初始化参数: region_id={self.region_id}, region_version={self.region_version}")
    
    def get_all_chat_infos(self):
        """获取所有聊天信息"""
        # 构建请求参数
        params = {
            "region_id": self.region_id,
            "region_version": self.region_version,
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.2.1"
        }
        
        # 使用用户抓包中的mtgsig参数
        mtgsig = {
            "a1": "1.2",
            "a2": int(time.time() * 1000),
            "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
            "a5": "KOd12vxO5t1OWl6XbN0EFslEdtCSjLNHGnp9BpWhtQkYm1gOqYlS",
            "a6": "hs1.6IkCZ+pY820bF4+wssW0FETM+dNEMIr22StSxAXNJlvhsMSC8iqTIA6og0zPnbixor62OJx/EIXIIaPaHz5V9+pbDMDcIaAj7TF4y1msg5hcWs7KuRUCI5WXS4eRy0/Zn",
            "a8": "1c77a18ab4812d1744fbe2054ee5386f",
            "a9": "3.2.1,7,99",
            "a10": "88",
            "x0": 4,
            "d1": "07e4fec211925cdc96860a7f1676a2a7"
        }
        params["mtgsig"] = json.dumps(mtgsig)
        
        # 构建请求体 - 使用表单格式
        data = {
            "types": json.dumps([
                {"type": 0, "pageSize": 40},
                {"type": 1, "pageSize": 20}
            ]),
            "timing": "1"
        }
        
        # 将数据转换为表单格式
        form_data = urllib.parse.urlencode(data)
        
        print(f"\n[getAllChatInfos] 请求URL: {self.base_url_all}")
        print(f"[getAllChatInfos] 请求参数: {params}")
        print(f"[getAllChatInfos] 请求体: {form_data}")
        
        try:
            response = requests.post(
                self.base_url_all, 
                params=params, 
                data=form_data,  # 使用表单数据
                headers=self.headers, 
                cookies=self.cookies,
                verify=False  # 禁用SSL证书验证
            )
            
            # 打印响应状态和内容
            print(f"[getAllChatInfos] 响应状态码: {response.status_code}")
            print(f"[getAllChatInfos] 响应头: {dict(response.headers)}")
            
            # 尝试解析响应内容
            try:
                response_json = response.json()
                print(f"[getAllChatInfos] 响应内容: {json.dumps(response_json, ensure_ascii=False)[:500]}...")
                return response_json
            except json.JSONDecodeError as e:
                print(f"[getAllChatInfos] JSON解析错误: {e}")
                print(f"[getAllChatInfos] 响应文本: {response.text[:500]}...")
                return None
            
        except Exception as e:
            print(f"[getAllChatInfos] 请求失败: {e}")
            return None
    
    def get_chat_infos_by_type(self, chat_type=2, page_size=20):
        """
        按类型获取聊天信息
        
        参数:
            chat_type: 聊天类型，2表示未读消息
            page_size: 每页大小
            
        返回:
            聊天信息
        """
        # 构建请求参数
        params = {
            "region_id": self.region_id,
            "region_version": self.region_version,
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.2.1"
        }
        
        # 使用用户抓包中的mtgsig参数
        mtgsig = {
            "a1": "1.2",
            "a2": int(time.time() * 1000),
            "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
            "a5": "KOd12vxO5t1OWl6XbN0EFslEdtCSjLNHGnp9BpWhtQkYm1gOqYlS",
            "a6": "hs1.6IkCZ+pY820bF4+wssW0FETM+dNEMIr22StSxAXNJlvhsMSC8iqTIA6og0zPnbixor62OJx/EIXIIaPaHz5V9+pbDMDcIaAj7TF4y1msg5hcWs7KuRUCI5WXS4eRy0/Zn",
            "a8": "1c77a18ab4812d1744fbe2054ee5386f",
            "a9": "3.2.1,7,99",
            "a10": "88",
            "x0": 4,
            "d1": "07e4fec211925cdc96860a7f1676a2a7"
        }
        params["mtgsig"] = json.dumps(mtgsig)
        
        # 构建请求体 - 使用表单格式
        type_data = {
            "type": chat_type,
            "pageSize": page_size
        }
        data = {
            "type": json.dumps(type_data)
        }
        
        # 将数据转换为表单格式
        form_data = urllib.parse.urlencode(data)
        
        print(f"\n[getChatInfosByType] 请求URL: {self.base_url_by_type}")
        print(f"[getChatInfosByType] 请求参数: {params}")
        print(f"[getChatInfosByType] 请求体: {form_data}")
        
        try:
            response = requests.post(
                self.base_url_by_type, 
                params=params, 
                data=form_data,  # 使用表单数据
                headers=self.headers, 
                cookies=self.cookies,
                verify=False  # 禁用SSL证书验证
            )
            
            # 打印响应状态和内容
            print(f"[getChatInfosByType] 响应状态码: {response.status_code}")
            print(f"[getChatInfosByType] 响应头: {dict(response.headers)}")
            
            # 尝试解析响应内容
            try:
                response_json = response.json()
                print(f"[getChatInfosByType] 响应内容: {json.dumps(response_json, ensure_ascii=False)[:500]}...")
                return response_json
            except json.JSONDecodeError as e:
                print(f"[getChatInfosByType] JSON解析错误: {e}")
                print(f"[getChatInfosByType] 响应文本: {response.text[:500]}...")
                return None
            
        except Exception as e:
            print(f"[getChatInfosByType] 请求失败: {e}")
            return None
    
    def get_unread_messages(self):
        """获取未读消息"""
        result = self.get_chat_infos_by_type(chat_type=2)
        if not result or result.get("code") != 0:
            print(f"获取未读消息失败: {result.get('msg') if result else '未知错误'}")
            return {
                "unread_count": 0,
                "chats": []
            }
        
        # 提取未读消息
        data = result.get("data", {})
        return {
            "unread_count": data.get("unreadCount", 0),
            "chats": data.get("chats", [])
        }
    
    def get_unread_count(self):
        """获取未读消息数量"""
        result = self.get_all_chat_infos()
        if not result or result.get("code") != 0:
            print(f"获取聊天信息失败: {result.get('msg') if result else '未知错误'}")
            return 0
        
        # 提取未读消息数量
        data = result.get("data", {})
        return data.get("totalUnreadCount", 0)
    
    def get_chat_list(self):
        """获取聊天列表"""
        result = self.get_all_chat_infos()
        if not result or result.get("code") != 0:
            print(f"获取聊天信息失败: {result.get('msg') if result else '未知错误'}")
            return []
        
        # 提取聊天列表
        data = result.get("data", {})
        all_chats_data = data.get("all", {})
        chats = all_chats_data.get("chats", [])
        
        return chats

# 解析cookie字符串为字典
def parse_cookies(cookies_str):
    """
    解析cookie字符串为字典
    
    参数:
        cookies_str: Cookie字符串
        
    返回:
        Cookie字典
    """
    cookie_dict = {}
    if not cookies_str:
        return cookie_dict
        
    for item in cookies_str.split(';'):
        if item.strip():
            try:
                key, value = item.strip().split('=', 1)
                cookie_dict[key.strip()] = value.strip()
            except ValueError:
                pass  # 忽略格式不正确的cookie项
    
    # 尝试从set_info中提取region_id和region_version
    if "set_info" in cookie_dict:
        try:
            set_info_str = cookie_dict.get("set_info", "{}")
            # 使用正则表达式提取region_id和region_version
            region_id_match = re.search(r'"region_id"\s*:\s*"([^"]+)"', set_info_str)
            region_version_match = re.search(r'"region_version"\s*:\s*(\d+)', set_info_str)
            
            if region_id_match and "region_id" not in cookie_dict:
                cookie_dict["region_id"] = region_id_match.group(1)
            if region_version_match and "region_version" not in cookie_dict:
                cookie_dict["region_version"] = region_version_match.group(1)
        except:
            pass
    
    # 打印解析后的cookie
    print(f"解析后的cookie: {json.dumps(cookie_dict, ensure_ascii=False)[:500]}...")
            
    return cookie_dict

# 示例用法
def get_chat_info(cookies):
    """
    获取美团外卖聊天信息
    
    参数:
        cookies: Cookie字典或字符串
        
    返回:
        聊天信息
    """
    # 如果cookies是字符串，转换为字典
    if isinstance(cookies, str):
        cookies = parse_cookies(cookies)
    
    # 创建聊天信息实例
    chat_info = MeituanChatInfo(cookies=cookies)
    
    # 获取未读消息
    unread_messages = chat_info.get_unread_messages()
    print(f"未读消息数量: {unread_messages['unread_count']}")
    print(f"未读消息列表数量: {len(unread_messages['chats'])}")
    
    # 获取聊天列表
    chat_list = chat_info.get_chat_list()
    print(f"聊天列表数量: {len(chat_list)}")
    
    return {
        "unread_messages": unread_messages,
        "chat_list": chat_list
    }

if __name__ == "__main__":
    # 示例cookie字符串
    example_cookie = "_lxsdk_cuid=19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8; token=06JKjVDdvx48fVY266Q07U9uBYFMIAQI4j19FmCKU0GI*; wmPoiId=27260079; acctId=229540584; region_id=1000420600; region_version=1743386677"
    
    # 获取聊天信息
    chat_info = get_chat_info(cookies=example_cookie)
    
    # 打印未读消息
    print("\n未读消息信息:")
    print(f"未读消息总数: {chat_info['unread_messages']['unread_count']}")
    
    # 打印未读消息列表的第一条消息(如果存在)
    if chat_info['unread_messages']['chats']:
        print("第一条未读消息:")
        print(json.dumps(chat_info['unread_messages']['chats'][0], ensure_ascii=False, indent=2))
    else:
        print("没有未读消息")
    
    # 打印聊天列表的第一条消息(如果存在)
    print("\n聊天列表信息:")
    if chat_info['chat_list']:
        print("第一条聊天信息:")
        print(json.dumps(chat_info['chat_list'][0], ensure_ascii=False, indent=2))
    else:
        print("没有聊天记录") 