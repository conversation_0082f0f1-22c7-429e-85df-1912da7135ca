<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import type { Ref } from 'vue'
import { ElMessage, ElMessageBox, ElConfigProvider } from 'element-plus'
// @ts-ignore
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
// @ts-ignore
import axios from 'axios'
axios.defaults.withCredentials = true

// 店铺类型声明
interface Shop {
  id: number
  shop_id: string
  login_account: string
  shop_name: string
  logo?: string
  auto_reply: number
  auto_comment: number
  auto_meal: number
  expire_time: string
  ck_status: number
  is_valid: number
  remark?: string
  reply_message_text?: string
  auto_comment_types?: string
  comment_good_text?: string
  comment_mid_text?: string
  comment_bad_text?: string
  auto_meal_periods?: string
  bind_time?: string
  updated_at?: string
  [key: string]: any
}

// 用户信息
interface UserInfo {
  id: number
  agent_account: string
  role: string
  balance: number
  [key: string]: any
}

const tableData: Ref<Shop[]> = ref([])
const allShopsData: Ref<Shop[]> = ref([]) // 存储所有店铺数据
const search = ref('')
const loading = ref(false)
const selectedShops: Ref<Shop[]> = ref([])
const originalTableData: Ref<Shop[]> = ref([])

// 用户信息
const userInfo = ref<UserInfo | null>(null)

// 检查是否为root用户
const isRootUser = computed(() => {
  return userInfo.value?.role === 'root'
})

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// Element Plus 中文配置
const locale = zhCn

// 弹窗状态
const replySettingVisible = ref(false)
const commentSettingVisible = ref(false)
const mealSettingVisible = ref(false)
const currentShop = ref<Shop | null>(null)

// 自动回复设定表单
const replySettingForm = ref({
  reply_message_text: '',
})

// 自动回评设定表单
const commentSettingForm = ref({
  auto_comment_types: [] as string[],
  comment_good_text: '',
  comment_mid_text: '',
  comment_bad_text: '',
})

// 自动出餐设定表单
interface MealPeriod {
  label: string
  start: string
  end: string
  duration: number // 单位：秒
}
const mealSettingForm = ref({
  periods: [{ label: '全天', start: '00:00', end: '23:59', duration: 300 }] as MealPeriod[], // 默认300秒
})

// 续费弹窗
const renewDialogVisible = ref(false)
const renewPoints = ref(1)
const renewShopTarget = ref<Shop | null>(null)

// 编辑过期时间弹窗
const editExpireDialogVisible = ref(false)
const editExpireShop = ref<Shop | null>(null)
const editExpireTime = ref('')

// 备注编辑弹窗
const editRemarkDialogVisible = ref(false)
const editRemarkShop = ref<Shop | null>(null)
const editRemarkText = ref('')

// 日志弹窗
const logsDialogVisible = ref(false)
const currentLogsShop = ref<Shop | null>(null)
const currentTaskType = ref<string>('')
const shopLogs = ref<any[]>([])
const logsLoading = ref(false)

// 获取店铺日志
const fetchShopLogs = async (shopId: number, taskType?: string) => {
  try {
    logsLoading.value = true
    const url = taskType
      ? `/api/shops/${shopId}/logs?task_type=${taskType}`
      : `/api/shops/${shopId}/logs`
    const response = await axios.get(url)
    if (response.data.code === 0) {
      shopLogs.value = response.data.data
    } else {
      ElMessage.error(response.data.message || '获取日志失败')
    }
  } catch (error) {
    console.error('获取店铺日志失败:', error)
    ElMessage.error('获取店铺日志失败')
  } finally {
    logsLoading.value = false
  }
}

// 打开任务日志
const viewTaskLogs = async (shop: Shop, taskType: string) => {
  currentLogsShop.value = shop
  currentTaskType.value = taskType
  logsDialogVisible.value = true
  await fetchShopLogs(shop.id, taskType)
}

// 获取任务类型中文名称
const getTaskTypeName = (taskType: string) => {
  const typeMap: Record<string, string> = {
    auto_reply: '自动回复',
    auto_comment: '自动回评',
    auto_meal: '自动出餐',
  }
  return typeMap[taskType] || taskType
}

// 从日志消息中提取出餐数量
const extractMealCount = (logMessage: string) => {
  const match = logMessage.match(/成功出餐\s*(\d+)\s*个订单/)
  return match ? match[1] : '0'
}

// 从日志消息中提取执行时间
const extractExecutionTime = (logMessage: string) => {
  const match = logMessage.match(/耗时:\s*([\d.]+)秒/)
  return match ? match[1] + 's' : '-'
}

// 从日志消息中提取订单详情
const extractOrderDetails = (logMessage: string) => {
  const detailsIndex = logMessage.indexOf('订单详情:')
  if (detailsIndex === -1) return logMessage

  const details = logMessage.substring(detailsIndex + 5).trim()
  // 格式化订单详情，使其更易读
  return details.replace(/订单\d+:/g, '\n$&').trim()
}

// 从日志消息中提取指定字段的值
const extractOrderField = (logMessage: string, fieldName: string) => {
  // 匹配字段名后面的值，支持中文冒号和英文冒号
  const regex = new RegExp(`${fieldName}[：:]\\s*([^\\n\\r]+)`)
  const match = logMessage.match(regex)
  return match ? match[1].trim() : '-'
}

// 格式化出餐用时
const formatMealTime = (timeStr: string) => {
  if (!timeStr || timeStr === '-') return '-'

  // 提取数字部分
  const match = timeStr.match(/(\d+)/)
  if (!match) return timeStr

  const seconds = parseInt(match[1])
  if (seconds < 60) {
    return `${seconds}秒`
  } else {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`
  }
}

// 打开店铺日志
const viewShopLogs = async (shop: Shop) => {
  currentLogsShop.value = shop
  currentTaskType.value = ''
  logsDialogVisible.value = true
  await fetchShopLogs(shop.id)
}

// 获取店铺列表
const fetchShops = async () => {
  try {
    loading.value = true
    const response = await axios.get('/api/shops')
    if (response.data.code === 0) {
      allShopsData.value = response.data.data as Shop[]
      originalTableData.value = response.data.data as Shop[]
      // 应用当前的搜索和分页
      applySearchAndPagination()
    } else {
      ElMessage.error(response.data.message || '获取店铺列表失败')
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error)
    ElMessage.error('获取店铺列表失败')
  } finally {
    loading.value = false
  }
}

// 应用搜索和分页
const applySearchAndPagination = () => {
  let filteredData = [...allShopsData.value]

  // 应用搜索过滤
  if (search.value.trim()) {
    const searchTerm = search.value.trim().toLowerCase()
    filteredData = filteredData.filter(
      (shop) =>
        shop.shop_name?.toLowerCase().includes(searchTerm) ||
        shop.shop_id?.toLowerCase().includes(searchTerm) ||
        shop.login_account?.toLowerCase().includes(searchTerm),
    )
  }

  // 应用CK状态过滤
  if (searchCkStatus.value) {
    if (searchCkStatus.value === 'valid') {
      filteredData = filteredData.filter((shop) => shop.ck_status === 1)
    } else if (searchCkStatus.value === 'invalid') {
      filteredData = filteredData.filter((shop) => shop.ck_status === 0)
    }
  }

  // 更新总数
  pagination.value.total = filteredData.length

  // 应用分页
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  tableData.value = filteredData.slice(start, end)
}

// 分页改变处理
const handlePageChange = (page: number) => {
  pagination.value.currentPage = page
  applySearchAndPagination()
}

// 每页大小改变处理
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1 // 重置到第一页
  applySearchAndPagination()
}

// 表格排序处理
const handleSortChange = ({ column, prop, order }: any) => {
  if (!order) {
    // 取消排序，恢复原始顺序
    applySearchAndPagination()
    return
  }

  // 对所有数据进行排序
  let sortedData = [...allShopsData.value]

  if (prop === 'expire_time') {
    sortedData.sort((a: Shop, b: Shop) => {
      const getTime = (row: Shop) =>
        row.expire_time ? new Date(row.expire_time.replace(/-/g, '/')).getTime() : 0
      const result = getTime(a) - getTime(b)
      return order === 'ascending' ? result : -result
    })
  } else if (prop === 'updated_at') {
    sortedData.sort((a: Shop, b: Shop) => {
      const getTime = (row: Shop) =>
        row.updated_at ? new Date(row.updated_at.replace(/-/g, '/')).getTime() : 0
      const result = getTime(a) - getTime(b)
      return order === 'ascending' ? result : -result
    })
  } else if (prop === 'login_expire') {
    sortedData.sort((a: Shop, b: Shop) => {
      const getDays = (row: Shop) => {
        if (!row.updated_at) return -9999
        const updateTime = new Date(row.updated_at.replace(/-/g, '/'))
        const now = new Date()
        const diffInDays = Math.floor(
          (now.getTime() - updateTime.getTime()) / (1000 * 60 * 60 * 24),
        )
        return 28 - diffInDays
      }
      const result = getDays(a) - getDays(b)
      return order === 'ascending' ? result : -result
    })
  }

  // 更新allShopsData并重新应用搜索和分页
  allShopsData.value = sortedData
  pagination.value.currentPage = 1 // 重置到第一页
  applySearchAndPagination()
}

// 续费店铺
const renewShop = async (shop: Shop) => {
  try {
    await ElMessageBox.confirm(
      `确定要为店铺"${shop.shop_name}"续费吗？\n将消耗1个积分，延长31天服务时间。`,
      '确认续费',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const response = await axios.post(`/api/shops/${shop.id}/renew`)
    if (response.data.code === 0) {
      ElMessage.success('续费成功')
      // 更新店铺到期时间
      shop.expire_time = response.data.data.new_expire_time
      // 刷新店铺列表
      await fetchShops()
    } else {
      ElMessage.error(response.data.message || '续费失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('续费失败:', error)
      ElMessage.error('续费失败')
    }
  }
}

// 打开自动任务开关
const toggleAutoTask = async (shop: Shop, taskType: string, val: boolean) => {
  try {
    const field = `auto_${taskType}`
    const newValue = val ? 1 : 0
    const response = await axios.put(`/api/shops/${shop.id}`, {
      [field]: newValue,
    })
    if (response.data.code === 0) {
      // 只用后端返回的最新 shop 数据更新当前行
      const updated = response.data.data
      Object.assign(shop, updated)
      ElMessage.success(
        `${taskType === 'reply' ? '自动回复' : taskType === 'comment' ? '自动回评' : '自动出餐'}${newValue ? '已开启' : '已关闭'}`,
      )
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    console.error('切换自动任务失败:', error)
    ElMessage.error('操作失败')
  }
}

// 打开自动回复设定
const openReplySetting = (shop: Shop) => {
  currentShop.value = shop
  replySettingForm.value.reply_message_text = shop.reply_message_text || ''
  replySettingVisible.value = true
}

// 保存自动回复设定
const saveReplySetting = async () => {
  if (!currentShop.value) return
  await axios.put(`/api/shops/${currentShop.value.id}`, {
    reply_message_text: replySettingForm.value.reply_message_text,
  })
  ElMessage.success('自动回复内容已保存')
  replySettingVisible.value = false
  await fetchShops()
}

// 打开自动回评设定
const openCommentSetting = (shop: Shop) => {
  currentShop.value = shop
  commentSettingForm.value.auto_comment_types = (shop.auto_comment_types || 'good,mid,bad').split(
    ',',
  )
  commentSettingForm.value.comment_good_text = shop.comment_good_text || ''
  commentSettingForm.value.comment_mid_text = shop.comment_mid_text || ''
  commentSettingForm.value.comment_bad_text = shop.comment_bad_text || ''
  commentSettingVisible.value = true
}

// 保存自动回评设定
const saveCommentSetting = async () => {
  if (!currentShop.value) return
  await axios.put(`/api/shops/${currentShop.value.id}`, {
    auto_comment_types: commentSettingForm.value.auto_comment_types.join(','),
    comment_good_text: commentSettingForm.value.comment_good_text,
    comment_mid_text: commentSettingForm.value.comment_mid_text,
    comment_bad_text: commentSettingForm.value.comment_bad_text,
  })
  ElMessage.success('自动回评内容已保存')
  commentSettingVisible.value = false
  await fetchShops()
}

// 打开自动出餐设定
const openMealSetting = (shop: Shop) => {
  currentShop.value = shop
  let periods: MealPeriod[] = [{ label: '全天', start: '00:00', end: '23:59', duration: 300 }]
  try {
    if (shop.auto_meal_periods) {
      const arr = JSON.parse(shop.auto_meal_periods)
      if (Array.isArray(arr) && arr.length > 0) periods = arr
    }
  } catch {}
  mealSettingForm.value.periods = periods
  mealSettingVisible.value = true
}

// 新增时段
const addMealPeriod = () => {
  mealSettingForm.value.periods.push({ label: '', start: '00:00', end: '23:59', duration: 300 })
}
// 删除时段
const removeMealPeriod = (idx: number) => {
  if (mealSettingForm.value.periods.length > 1) mealSettingForm.value.periods.splice(idx, 1)
}

// 保存自动出餐设定
const saveMealSetting = async () => {
  if (!currentShop.value) return
  await axios.put(`/api/shops/${currentShop.value.id}`, {
    auto_meal_periods: JSON.stringify(mealSettingForm.value.periods),
  })
  ElMessage.success('自动出餐时段已保存')
  mealSettingVisible.value = false
  await fetchShops()
}

// 打开续费弹窗
const openRenewDialog = (shop: Shop) => {
  renewShopTarget.value = shop
  renewPoints.value = 1
  renewDialogVisible.value = true
}

// 确认续费
const confirmRenewShop = async () => {
  if (!renewShopTarget.value) return
  try {
    const response = await axios.post(`/api/shops/${renewShopTarget.value.id}/renew`, {
      points: renewPoints.value,
    })
    if (response.data.code === 0) {
      ElMessage.success('续费成功')
      renewShopTarget.value.expire_time = response.data.data.new_expire_time
      await fetchShops()
      renewDialogVisible.value = false
    } else {
      ElMessage.error(response.data.message || '续费失败')
    }
  } catch (error) {
    ElMessage.error('续费失败')
  }
}

// 打开编辑过期时间弹窗
const openEditExpireDialog = (shop: Shop) => {
  editExpireShop.value = shop
  editExpireTime.value = shop.expire_time ? shop.expire_time.split(' ')[0] : ''
  editExpireDialogVisible.value = true
}

// 保存过期时间
const saveExpireTime = async () => {
  if (!editExpireShop.value || !editExpireTime.value) return

  try {
    const response = await axios.put(`/api/shops/${editExpireShop.value.id}/expire`, {
      expire_time: editExpireTime.value + ' 23:59:59',
    })

    if (response.data.code === 0) {
      ElMessage.success('过期时间更新成功')
      editExpireShop.value.expire_time = response.data.data.expire_time
      editExpireDialogVisible.value = false
      await fetchShops()
    } else {
      ElMessage.error(response.data.message || '更新失败')
    }
  } catch (error) {
    console.error('更新过期时间失败:', error)
    ElMessage.error('更新失败')
  }
}

// 打开备注编辑弹窗
const openEditRemarkDialog = (shop: Shop) => {
  editRemarkShop.value = shop
  editRemarkText.value = shop.remark || ''
  editRemarkDialogVisible.value = true
}

// 保存备注
const saveRemark = async () => {
  if (!editRemarkShop.value) return

  try {
    const response = await axios.put(`/api/shops/${editRemarkShop.value.id}`, {
      remark: editRemarkText.value,
    })

    if (response.data.code === 0) {
      ElMessage.success('备注更新成功')
      editRemarkShop.value.remark = editRemarkText.value
      editRemarkDialogVisible.value = false
      await fetchShops()
    } else {
      ElMessage.error(response.data.message || '更新失败')
    }
  } catch (error) {
    console.error('更新备注失败:', error)
    ElMessage.error('更新失败')
  }
}

// 删除店铺
const deleteShop = async (shop: Shop) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除门店“${shop.shop_name}”吗？删除后不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
    const response = await axios.delete(`/api/shops/${shop.id}`)
    if (response.data.code === 0) {
      ElMessage.success('删除成功')
      await fetchShops()
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 编辑店铺
const editShop = (shop: Shop) => {
  // TODO: 实现编辑店铺功能
  ElMessage.info('编辑功能开发中...')
}

// 刷新数据
const refreshData = async () => {
  await fetchShops()
  ElMessage.success('数据已刷新')
}

// 搜索店铺
const searchShopId = ref('')
const searchCkStatus = ref('')

const searchShops = () => {
  pagination.value.currentPage = 1 // 重置到第一页
  applySearchAndPagination()
}

// 重置搜索
const resetSearch = () => {
  search.value = ''
  searchShopId.value = ''
  searchCkStatus.value = ''
  pagination.value.currentPage = 1 // 重置到第一页
  applySearchAndPagination()
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '未设置'
  return timeStr.replace(' ', '\n')
}

// 检查是否过期
const isExpired = (expireTime: string) => {
  if (!expireTime) return true
  return new Date(expireTime) < new Date()
}

// 检查是否可操作（未过期且ck_status=1）
const isOperable = (shop: Shop) => {
  return !isExpired(shop.expire_time) && shop.ck_status === 1
}

// 获取ck状态文本
const getCkStatusText = (shop: Shop) => {
  return shop.ck_status === 1 ? '有效' : '无效'
}

// 获取状态类型
const getStatusType = (shop: Shop) => {
  if (!shop.is_valid) return 'danger'
  if (!shop.ck_status) return 'warning'
  return 'success'
}

// 计算登录过期时长（天）
const getCookieExpireDays = (shop: Shop) => {
  if (!shop.updated_at) return '未知'

  // 解析updated_at时间，支持多种格式
  let updateTime: Date
  try {
    // 处理可能的时间格式：YYYY-MM-DD HH:mm:ss 或 YYYY-MM-DD
    const timeStr = shop.updated_at.replace(/-/g, '/')
    updateTime = new Date(timeStr)

    // 检查日期是否有效
    if (isNaN(updateTime.getTime())) {
      return '未知'
    }
  } catch (error) {
    return '未知'
  }

  const now = new Date()
  // 计算当前时间减去updated_at得到的天数
  const diffInMs = now.getTime() - updateTime.getTime()
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))

  // 28天减去已过去的天数就是剩余天数
  const remainingDays = 28 - diffInDays

  return remainingDays > 0 ? `${remainingDays}天` : '已过期'
}

// 复制文本，支持 clipboard API 和降级方案
function copyText(text: string) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(
      () => {
        ElMessage.success('店铺信息已复制')
      },
      () => fallbackCopyText(text),
    )
  } else {
    fallbackCopyText(text)
  }
}
function fallbackCopyText(text: string) {
  const textarea = document.createElement('textarea')
  textarea.value = text
  textarea.style.position = 'fixed'
  textarea.style.opacity = '0'
  document.body.appendChild(textarea)
  textarea.focus()
  textarea.select()
  try {
    document.execCommand('copy')
    ElMessage.success('店铺信息已复制')
  } catch (err) {
    ElMessage.error('复制失败')
  }
  document.body.removeChild(textarea)
}
// 复制店铺信息
const copyShopInfo = (shop: Shop) => {
  const remark = shop.remark ? shop.remark : '无'
  const expireTime = shop.expire_time ? shop.expire_time.split(' ')[0] : '未设置'

  const text = `外卖助手：美团外卖${remark || ''}
门店名称：${shop.shop_name}
门店标识：${shop.shop_id}
到期时间：${expireTime}`

  copyText(text)
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const response = await axios.get('/api/auth/profile')
    if (response.data.code === 0) {
      userInfo.value = response.data.data
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 备用方案：从 localStorage 获取用户信息
    try {
      const storedUserInfo = localStorage.getItem('userInfo')
      if (storedUserInfo) {
        userInfo.value = JSON.parse(storedUserInfo)
      }
    } catch (localError) {
      console.error('从localStorage获取用户信息失败:', localError)
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchShops()
  fetchUserInfo()
})

// 组件卸载时清理定时器
onUnmounted(() => {})
</script>

<template>
  <div class="meituan-container">
    <el-card class="page-header-card">
      <div class="page-header">
        <div class="header-title">
          <el-icon class="header-icon"><ShoppingCart /></el-icon>
          <h2>美团管理</h2>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="refreshData" :loading="loading">
            <el-icon><RefreshRight /></el-icon>
            刷新
          </el-button>
          <el-tag
            v-if="userInfo"
            :type="isRootUser ? 'danger' : 'info'"
            size="small"
            style="margin-left: 10px"
          >
            {{ isRootUser ? '管理员' : '普通用户' }}
          </el-tag>
        </div>
      </div>
    </el-card>

    <el-card class="content-card search-card">
      <div class="search-section">
        <el-form :inline="true">
          <el-form-item label="门店名称">
            <el-input v-model="search" placeholder="输入门店名称" clearable />
          </el-form-item>
          <el-form-item label="门店ID">
            <el-input v-model="searchShopId" placeholder="输入门店ID" clearable />
          </el-form-item>
          <el-form-item label="ck状态">
            <el-select v-model="searchCkStatus" placeholder="全部" style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="有效" value="valid" />
              <el-option label="无效" value="invalid" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchShops">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card class="content-card">
      <div class="action-section">
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
      </div>

      <div class="table-section">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          stripe
          highlight-current-row
          v-loading="loading"
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333', fontWeight: 'bold' }"
          @selection-change="selectedShops = $event"
          @sort-change="handleSortChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="shop_id" label="门店ID" width="150" />
          <el-table-column prop="login_account" label="登录帐号" width="150" />
          <el-table-column prop="shop_name" label="店名" show-overflow-tooltip />
          <el-table-column prop="remark" label="备注" width="200" show-overflow-tooltip>
            <template #default="scope">
              <div style="display: flex; align-items: center; gap: 8px">
                <span
                  style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                >
                  {{ scope.row.remark || '-' }}
                </span>
                <el-button
                  type="text"
                  size="small"
                  @click="openEditRemarkDialog(scope.row)"
                  style="color: #409eff"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="ck状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.ck_status === 1 ? 'success' : 'danger'" size="small">
                {{ getCkStatusText(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="Logo" width="80">
            <template #default="scope">
              <el-avatar
                shape="square"
                :size="40"
                :src="scope.row.logo || '/src/assets/logo.svg'"
              />
            </template>
          </el-table-column>
          <el-table-column label="自动回复" width="130">
            <template #default="scope">
              <el-switch
                :model-value="!!scope.row.auto_reply"
                active-color="#13ce66"
                :disabled="false"
                @change="(val: boolean) => toggleAutoTask(scope.row, 'reply', val)"
              />
              <el-button type="text" size="small" @click="openReplySetting(scope.row)">
                <el-icon><Setting /></el-icon>
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="viewTaskLogs(scope.row, 'auto_reply')"
                style="color: #409eff"
              >
                <el-icon><Document /></el-icon>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="自动回评" width="130">
            <template #default="scope">
              <el-switch
                :model-value="!!scope.row.auto_comment"
                active-color="#13ce66"
                :disabled="false"
                @change="(val: boolean) => toggleAutoTask(scope.row, 'comment', val)"
              />
              <el-button type="text" size="small" @click="openCommentSetting(scope.row)">
                <el-icon><Setting /></el-icon>
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="viewTaskLogs(scope.row, 'auto_comment')"
                style="color: #e6a23c"
              >
                <el-icon><Document /></el-icon>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="自动出餐" width="130">
            <template #default="scope">
              <el-switch
                :model-value="!!scope.row.auto_meal"
                active-color="#13ce66"
                :disabled="false"
                @change="(val: boolean) => toggleAutoTask(scope.row, 'meal', val)"
              />
              <el-button type="text" size="small" @click="openMealSetting(scope.row)">
                <el-icon><Setting /></el-icon>
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="viewTaskLogs(scope.row, 'auto_meal')"
                style="color: #909399"
              >
                <el-icon><Document /></el-icon>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="到期时间" width="200" prop="expire_time" sortable="custom">
            <template #default="scope">
              <div class="time-info">
                <div class="time-item">
                  <el-icon><Timer /></el-icon>
                  <span :class="{ expired: isExpired(scope.row.expire_time) }">
                    {{ formatTime(scope.row.expire_time) }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="CK登录时间" width="200" prop="updated_at" sortable="custom">
            <template #default="scope">
              <div class="time-info">
                <div class="time-item">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ formatTime(scope.row.updated_at) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="登录过期" width="120" prop="login_expire" sortable="custom">
            <template #default="scope">
              <span
                :style="
                  getCookieExpireDays(scope.row) === '已过期'
                    ? 'color:#f56c6c;font-weight:bold'
                    : ''
                "
                >{{ getCookieExpireDays(scope.row) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="scope">
              <el-button
                type="danger"
                size="small"
                :disabled="false"
                @click="openRenewDialog(scope.row)"
              >
                <el-icon><Coin /></el-icon>
                续费
              </el-button>
              <el-button
                v-if="isRootUser"
                type="warning"
                size="small"
                @click="openEditExpireDialog(scope.row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="primary" size="small" plain @click="copyShopInfo(scope.row)">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
              <el-button type="info" size="small" plain @click="viewShopLogs(scope.row)">
                <el-icon><Document /></el-icon>
                日志
              </el-button>
              <el-button type="danger" size="small" plain @click="deleteShop(scope.row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pagination.pageSize"
            :current-page="pagination.currentPage"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 自动回复设定弹窗 -->
    <el-dialog
      title="自动回复设定"
      v-model="replySettingVisible"
      width="500px"
      :before-close="() => (replySettingVisible = false)"
    >
      <el-form :model="replySettingForm" label-width="100px">
        <el-form-item label="回复内容">
          <el-input
            type="textarea"
            v-model="replySettingForm.reply_message_text"
            :rows="4"
            placeholder="请输入自动回复内容"
          />
          <p class="text-muted">
            当前内容：{{ replySettingForm.reply_message_text || '使用系统默认' }}
          </p>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="replySettingVisible = false">取消</el-button>
          <el-button type="primary" @click="saveReplySetting">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 自动回评设定弹窗 -->
    <el-dialog
      title="自动回评设定"
      v-model="commentSettingVisible"
      width="500px"
      :before-close="() => (commentSettingVisible = false)"
    >
      <el-form :model="commentSettingForm" label-width="100px">
        <el-form-item label="类型">
          <el-checkbox-group v-model="commentSettingForm.auto_comment_types">
            <el-checkbox label="good">好评</el-checkbox>
            <el-checkbox label="mid">中评</el-checkbox>
            <el-checkbox label="bad">差评</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="好评内容">
          <el-input
            type="textarea"
            v-model="commentSettingForm.comment_good_text"
            :rows="2"
            placeholder="请输入好评内容"
          />
        </el-form-item>
        <el-form-item label="中评内容">
          <el-input
            type="textarea"
            v-model="commentSettingForm.comment_mid_text"
            :rows="2"
            placeholder="请输入中评内容"
          />
        </el-form-item>
        <el-form-item label="差评内容">
          <el-input
            type="textarea"
            v-model="commentSettingForm.comment_bad_text"
            :rows="2"
            placeholder="请输入差评内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="commentSettingVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCommentSetting">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 自动出餐设定弹窗 -->
    <el-dialog
      title="自动出餐设定"
      v-model="mealSettingVisible"
      width="600px"
      :before-close="() => (mealSettingVisible = false)"
    >
      <el-form :model="mealSettingForm" label-width="100px">
        <div
          v-for="(period, idx) in mealSettingForm.periods"
          :key="idx"
          style="display: flex; align-items: center; margin-bottom: 8px; gap: 8px"
        >
          <el-input
            v-model="period.label"
            placeholder="时段名称"
            style="width: 80px"
            :disabled="period.label === '全天'"
          />
          <el-time-picker
            v-model="period.start"
            placeholder="开始"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 90px"
            :disabled="period.label === '全天'"
          />
          <span>至</span>
          <el-time-picker
            v-model="period.end"
            placeholder="结束"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 90px"
            :disabled="period.label === '全天'"
          />
          <el-input-number v-model="period.duration" :min="1" :max="86400" style="width: 120px" />
          秒
          <el-button
            icon="Plus"
            @click="addMealPeriod"
            v-if="idx === mealSettingForm.periods.length - 1"
            circle
            size="small"
          />
          <el-button
            icon="Delete"
            @click="removeMealPeriod(idx)"
            circle
            size="small"
            :disabled="mealSettingForm.periods.length === 1 || period.label === '全天'"
          />
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="mealSettingVisible = false">取消</el-button>
          <el-button type="primary" @click="saveMealSetting">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 续费弹窗 -->
    <el-dialog
      title="续费"
      v-model="renewDialogVisible"
      width="400px"
      :before-close="() => (renewDialogVisible = false)"
    >
      <el-form label-width="100px">
        <el-form-item label="续费月数(积分)">
          <el-input-number v-model="renewPoints" :min="1" :max="12" />
          <span style="margin-left: 8px">（1积分=31天，最多12个月）</span>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmRenewShop">确认续费</el-button>
          <el-button @click="renewDialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 编辑过期时间弹窗 -->
    <el-dialog
      title="编辑过期时间"
      v-model="editExpireDialogVisible"
      width="400px"
      :before-close="() => (editExpireDialogVisible = false)"
    >
      <el-config-provider :locale="locale">
        <el-form label-width="100px">
          <el-form-item label="过期时间">
            <el-date-picker
              v-model="editExpireTime"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="saveExpireTime">确认</el-button>
            <el-button @click="editExpireDialogVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-config-provider>
    </el-dialog>

    <!-- 备注编辑弹窗 -->
    <el-dialog
      title="编辑备注"
      v-model="editRemarkDialogVisible"
      width="500px"
      :before-close="() => (editRemarkDialogVisible = false)"
    >
      <el-form :model="editRemarkShop" label-width="100px">
        <el-form-item label="备注">
          <el-input type="textarea" v-model="editRemarkText" :rows="4" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editRemarkDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRemark">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 店铺日志弹窗 -->
    <el-dialog
      :title="
        currentTaskType
          ? `${currentLogsShop?.shop_name} - ${getTaskTypeName(currentTaskType)}日志`
          : '店铺日志'
      "
      v-model="logsDialogVisible"
      width="800px"
      :before-close="() => (logsDialogVisible = false)"
      v-loading="logsLoading"
    >
      <div v-if="currentLogsShop" style="margin-bottom: 16px">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="店铺名称">{{
            currentLogsShop.shop_name
          }}</el-descriptions-item>
          <el-descriptions-item label="店铺ID">{{ currentLogsShop.shop_id }}</el-descriptions-item>
          <el-descriptions-item v-if="currentTaskType" label="任务类型">{{
            getTaskTypeName(currentTaskType)
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <el-table
        :data="shopLogs"
        border
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333', fontWeight: 'bold' }"
      >
        <el-table-column prop="task_type" label="任务类型" width="120">
          <template #default="scope">
            <el-tag
              :type="
                scope.row.task_type === 'auto_reply'
                  ? 'success'
                  : scope.row.task_type === 'auto_comment'
                    ? 'warning'
                    : scope.row.task_type === 'auto_meal'
                      ? 'info'
                      : 'primary'
              "
              size="small"
            >
              {{ getTaskTypeName(scope.row.task_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="currentTaskType !== 'auto_meal'"
          prop="log_message"
          label="日志消息"
          show-overflow-tooltip
        />
        <!-- 自动出餐专用的订单详情列显示 -->
        <template v-if="currentTaskType === 'auto_meal'">
          <el-table-column label="订单号" width="180">
            <template #default="scope">
              {{ extractOrderField(scope.row.log_message, '订单号') }}
            </template>
          </el-table-column>
          <el-table-column label="日序号" width="80">
            <template #default="scope">
              {{ extractOrderField(scope.row.log_message, '日序号') }}
            </template>
          </el-table-column>
          <el-table-column label="订单类型" width="100">
            <template #default="scope">
              <el-tag
                :type="
                  extractOrderField(scope.row.log_message, '订单类型') === '预订单'
                    ? 'warning'
                    : 'info'
                "
                size="small"
              >
                {{ extractOrderField(scope.row.log_message, '订单类型') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="出餐用时" width="100">
            <template #default="scope">
              {{ formatMealTime(extractOrderField(scope.row.log_message, '出餐用时')) }}
            </template>
          </el-table-column>
          <el-table-column label="下单时间" width="160">
            <template #default="scope">
              {{ extractOrderField(scope.row.log_message, '下单时间') }}
            </template>
          </el-table-column>
          <el-table-column label="出餐时间" width="160">
            <template #default="scope">
              {{ extractOrderField(scope.row.log_message, '出餐时间') }}
            </template>
          </el-table-column>
        </template>
      </el-table>
      <div v-if="shopLogs.length === 0" style="text-align: center; padding: 40px; color: #909399">
        暂无日志记录
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.meituan-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  margin: 0;
  padding: 0;
}

.page-header-card {
  margin-bottom: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 22px;
  margin-right: 8px;
  color: #1890ff;
}

.header-title h2 {
  font-size: 18px;
  color: #333;
  margin: 0;
  font-weight: 600;
}

.content-card {
  margin-bottom: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.search-card {
  background-color: #fff;
}

.search-section {
  padding: 8px 0;
}

.action-section {
  margin-bottom: 16px;
  display: flex;
  gap: 10px;
}

.table-section {
  width: 100%;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.time-item {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.time-item .el-icon {
  margin-right: 5px;
  color: #909399;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-button .el-icon) {
  margin-right: 4px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-card__body) {
  padding: 16px;
}

.expired {
  color: #f56c6c;
  font-weight: bold;
}

.time-item .el-icon {
  margin-right: 5px;
  color: #909399;
}

.time-item span {
  font-size: 13px;
  line-height: 1.4;
}

:deep(.el-switch.is-disabled) {
  opacity: 0.6;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-button.is-disabled) {
  opacity: 0.6;
}

/* 日志按钮悬停效果 */
:deep(.el-button[style*='color: #409eff']:hover) {
  background-color: #ecf5ff;
  border-color: #409eff;
}

:deep(.el-button[style*='color: #e6a23c']:hover) {
  background-color: #fdf6ec;
  border-color: #e6a23c;
}

:deep(.el-button[style*='color: #909399']:hover) {
  background-color: #f4f4f5;
  border-color: #909399;
}

/* 备注编辑按钮悬停效果 */
:deep(.el-button[style*='color: #409eff']:hover) {
  background-color: #ecf5ff;
  border-color: #409eff;
}
</style>
