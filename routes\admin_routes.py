#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员专用路由 - 使用不常见命名避免暴露
"""

from flask import Blueprint, request, jsonify
from utils.db_utils import get_connection
import hashlib

# 使用不常见的命名
admin_bp = Blueprint('admin', __name__)

# 管理员验证装饰器
def require_admin_auth(f):
    def admin_decorated_function(*args, **kwargs):
        # 简单的管理员验证
        admin_token = request.headers.get('X-Admin-Token')
        if not admin_token or admin_token != 'meituan_admin_2024_secure':
            return jsonify({
                'code': 1,
                'message': '无权限访问',
                'data': None
            }), 403
        return f(*args, **kwargs)
    admin_decorated_function.__name__ = f.__name__
    return admin_decorated_function

@admin_bp.route('/login', methods=['POST'])
def admin_login():
    """管理员登录"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        
        # 检查必填字段
        if not data or 'admin_account' not in data or 'admin_password' not in data:
            return jsonify({
                'code': 1,
                'message': '请输入账号和密码',
                'data': None
            }), 400
        
        admin_account = data['admin_account']
        admin_password = data['admin_password']
        
        # 密码加密（使用MD5）
        password_hash = hashlib.md5(admin_password.encode('utf-8')).hexdigest()
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询管理员信息
        query = """
        SELECT id, admin_account, admin_name, role, status
        FROM admins 
        WHERE admin_account = %s AND admin_password = %s AND status = 1
        """
        cursor.execute(query, [admin_account, password_hash])
        admin = cursor.fetchone()
        
        if not admin:
            return jsonify({
                'code': 1,
                'message': '账号或密码错误',
                'data': None
            }), 401
        
        return jsonify({
            'code': 0,
            'message': '登录成功',
            'data': {
                'admin': admin,
                'token': f"admin_{admin['id']}"
            }
        })
        
    except Exception as e:
        print(f"管理员登录失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'登录失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@admin_bp.route('/agent_list', methods=['GET'])
@require_admin_auth
def get_agent_list():
    """获取代理列表"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT id, agent_account, balance, created_at, updated_at
        FROM agents
        ORDER BY created_at DESC
        """
        cursor.execute(query)
        agents = cursor.fetchall()
        
        # 处理时间格式
        for agent in agents:
            if agent['created_at']:
                agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if agent['updated_at']:
                agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                    # 转换balance为整数
        if agent['balance']:
            agent['balance'] = int(agent['balance'])
        
        return jsonify({
            'code': 0,
            'message': '获取成功',
            'data': agents
        })
    except Exception as e:
        print(f"获取代理列表失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'获取失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@admin_bp.route('/agent_create', methods=['POST'])
@require_admin_auth
def create_agent():
    """创建代理"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        
        # 检查必填字段
        required_fields = ['agent_account', 'agent_password']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'code': 1,
                    'message': f'缺少必填字段: {field}',
                    'data': None
                }), 400
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查代理账号是否已存在
        check_query = "SELECT id FROM agents WHERE agent_account = %s"
        cursor.execute(check_query, [data['agent_account']])
        existing_agent = cursor.fetchone()
        
        if existing_agent:
            return jsonify({
                'code': 1,
                'message': '代理账号已存在',
                'data': None
            }), 400
        
        # 密码加密
        password_hash = hashlib.md5(data['agent_password'].encode('utf-8')).hexdigest()
        
        # 创建新代理
        insert_query = """
        INSERT INTO agents (agent_account, agent_password, balance, created_at, updated_at)
        VALUES (%s, %s, %s, NOW(), NOW())
        """
        
        cursor.execute(insert_query, [
            data['agent_account'],
            password_hash,
            data.get('balance', 0.00)
        ])
        
        connection.commit()
        
        # 获取创建的代理信息
        agent_id = cursor.lastrowid
        cursor.execute("SELECT * FROM agents WHERE id = %s", [agent_id])
        agent = cursor.fetchone()
        
        # 处理时间格式
        if agent['created_at']:
            agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if agent['updated_at']:
            agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        # 转换balance为float
        if agent['balance']:
            agent['balance'] = float(agent['balance'])
        
        return jsonify({
            'code': 0,
            'message': '创建成功',
            'data': agent
        })
    except Exception as e:
        print(f"创建代理失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'创建失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@admin_bp.route('/agent_update/<int:agent_id>', methods=['PUT'])
@require_admin_auth
def update_agent(agent_id):
    """更新代理信息"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查代理是否存在
        check_query = "SELECT id FROM agents WHERE id = %s"
        cursor.execute(check_query, [agent_id])
        existing_agent = cursor.fetchone()
        
        if not existing_agent:
            return jsonify({
                'code': 1,
                'message': '代理不存在',
                'data': None
            }), 404
        
        # 构建更新查询
        update_fields = []
        update_values = []
        
        fields_to_update = ['balance']
        
        for field in fields_to_update:
            if field in data:
                update_fields.append(f"{field} = %s")
                update_values.append(data[field])
        
        # 处理密码更新
        if 'agent_password' in data:
            password_hash = hashlib.md5(data['agent_password'].encode('utf-8')).hexdigest()
            update_fields.append("agent_password = %s")
            update_values.append(password_hash)
        
        if not update_fields:
            return jsonify({
                'code': 1,
                'message': '没有提供有效的更新字段',
                'data': None
            }), 400
        
        # 添加更新时间
        update_fields.append("updated_at = NOW()")
        
        # 执行更新
        update_query = f"UPDATE agents SET {', '.join(update_fields)} WHERE id = %s"
        update_values.append(agent_id)
        
        cursor.execute(update_query, update_values)
        connection.commit()
        
        # 获取更新后的代理信息
        cursor.execute("SELECT * FROM agents WHERE id = %s", [agent_id])
        agent = cursor.fetchone()
        
        # 处理时间格式
        if agent['created_at']:
            agent['created_at'] = agent['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if agent['updated_at']:
            agent['updated_at'] = agent['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        # 转换balance为float
        if agent['balance']:
            agent['balance'] = float(agent['balance'])
        
        return jsonify({
            'code': 0,
            'message': '更新成功',
            'data': agent
        })
    except Exception as e:
        print(f"更新代理失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'更新失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@admin_bp.route('/agent_delete/<int:agent_id>', methods=['DELETE'])
@require_admin_auth
def delete_agent(agent_id):
    """删除代理"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查代理是否存在
        check_query = "SELECT id FROM agents WHERE id = %s"
        cursor.execute(check_query, [agent_id])
        existing_agent = cursor.fetchone()
        
        if not existing_agent:
            return jsonify({
                'code': 1,
                'message': '代理不存在',
                'data': None
            }), 404
        
        # 执行删除
        delete_query = "DELETE FROM agents WHERE id = %s"
        cursor.execute(delete_query, [agent_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '删除成功',
            'data': None
        })
    except Exception as e:
        print(f"删除代理失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'删除失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@admin_bp.route('/system_stats', methods=['GET'])
@require_admin_auth
def get_system_stats():
    """获取系统统计信息"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 统计代理数量
        cursor.execute("SELECT COUNT(*) as agent_count FROM agents")
        agent_count = cursor.fetchone()['agent_count']
        
        # 统计店铺数量
        cursor.execute("SELECT COUNT(*) as shop_count FROM shops")
        shop_count = cursor.fetchone()['shop_count']
        
        # 统计总积分
        cursor.execute("SELECT SUM(balance) as total_balance FROM agents")
        total_balance = cursor.fetchone()['total_balance'] or 0
        
        return jsonify({
            'code': 0,
            'message': '获取成功',
            'data': {
                'agent_count': agent_count,
                'shop_count': shop_count,
                'total_balance': float(total_balance)
            }
        })
    except Exception as e:
        print(f"获取系统统计失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'获取失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 