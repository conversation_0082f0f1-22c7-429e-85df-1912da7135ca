#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
美团登录器GUI界面 - 现代化版本 (CustomTkinter)
支持多线程并发登录
"""

import sys
import json
import requests
import threading
import pickle
import os
from datetime import datetime
import customtkinter as ctk
from tkinter import messagebox
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue

from meituan_login_cdp import MeituanLoginCDP

# 设置CustomTkinter主题
ctk.set_appearance_mode("light")  # 浅色主题
ctk.set_default_color_theme("green")  # 绿色主题

API_BASE = "http://[2409:8a44:5877:6f20:4a9:38ab:8b92:af49]:5000"

# 全局线程池
executor = ThreadPoolExecutor(max_workers=20)  # 最多20个并发登录
login_queue = queue.Queue()  # 登录队列

class LoginDialog(ctk.CTkToplevel):
    """登录对话框"""
    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.agent_info = None
        self.result = None
        self.login_success = False
        
        # 配置窗口
        self.title("登录器登录")
        self.geometry("450x500")
        self.resizable(False, False)
        
        # 设置模态
        self.transient(parent)
        self.grab_set()
        
        self.init_ui()
        
    def init_ui(self):
        """初始化登录界面"""
        # 主容器
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="美团登录器",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 副标题
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="请输入代理账号和密码",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        subtitle_label.pack(pady=(0, 30))
        
        # 登录表单
        form_frame = ctk.CTkFrame(main_frame)
        form_frame.pack(fill="x", padx=20, pady=10)
        
        # 账号输入
        ctk.CTkLabel(form_frame, text="代理账号:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=20, pady=(20, 5))
        self.account_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="请输入代理账号",
            width=350,
            height=45,
            font=ctk.CTkFont(size=16)
        )
        self.account_entry.pack(pady=(0, 20))
        
        # 密码输入
        ctk.CTkLabel(form_frame, text="密码:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=20, pady=(0, 5))
        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="请输入密码",
            show="*",
            width=350,
            height=45,
            font=ctk.CTkFont(size=16)
        )
        self.password_entry.pack(pady=(0, 30))
        
        # 按钮框架
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=20)
        
        # 取消按钮
        self.cancel_button = ctk.CTkButton(
            button_frame,
            text="取消",
            width=120,
            height=40,
            font=ctk.CTkFont(size=14),
            command=self.cancel,
            fg_color="gray",
            hover_color="darkgray"
        )
        self.cancel_button.pack(side="left", padx=(0, 10))
        
        # 登录按钮
        self.login_button = ctk.CTkButton(
            button_frame,
            text="登录",
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.handle_login
        )
        self.login_button.pack(side="right")
        
        # 状态显示
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="",
            font=ctk.CTkFont(size=12),
            text_color="red"
        )
        self.status_label.pack(pady=10)
        
        # 回车登录
        self.account_entry.bind("<Return>", lambda e: self.handle_login())
        self.password_entry.bind("<Return>", lambda e: self.handle_login())
        
    def handle_login(self):
        """处理登录"""
        account = self.account_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not account or not password:
            self.status_label.configure(text="请输入账号和密码", text_color="red")
            return
            
        self.login_button.configure(state="disabled", text="登录中...")
        self.status_label.configure(text="正在验证账号...", text_color="blue")
        
        # 在新线程中执行登录
        def login_thread():
            try:
                response = requests.post(
                    f"{API_BASE}/api/auth/login",
                    json={
                        'agent_account': account,
                        'agent_password': password
                    },
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                result = response.json()
                
                if result['code'] == 0:
                    # 使用after确保在主线程中执行UI更新
                    self.after(0, lambda: self.on_login_success(result['data']['agent']))
                else:
                    self.after(0, lambda: self.on_login_error(result['message'] or "登录失败"))
                    
            except Exception as e:
                self.after(0, lambda: self.on_login_error(f"登录失败: {str(e)}"))
                
        threading.Thread(target=login_thread, daemon=True).start()
        
    def on_login_success(self, agent_info):
        """登录成功"""
        # 保存原始密码用于后续验证
        agent_info['agent_password'] = self.password_entry.get().strip()
        self.agent_info = agent_info
        self.login_success = True
        self.status_label.configure(text="登录成功", text_color="green")
        # 延迟关闭对话框，让用户看到成功信息
        self.after(1000, self.close_success)
        
    def close_success(self):
        """成功关闭对话框"""
        messagebox.showinfo("登录成功", f"欢迎，{self.agent_info['agent_account']}！")
        self.result = True
        self.destroy()
        
    def on_login_error(self, error):
        """登录失败"""
        self.status_label.configure(text=error, text_color="red")
        self.login_button.configure(state="normal", text="登录")
        
    def cancel(self):
        """取消登录"""
        self.destroy()

class LoginThread(threading.Thread):
    """登录线程"""
    def __init__(self, task_id, agent_info, progress_callback, success_callback, error_callback):
        super().__init__()
        self.task_id = task_id
        self.agent_info = agent_info
        self.progress_callback = progress_callback
        self.success_callback = success_callback
        self.error_callback = error_callback
        self.daemon = True
    
    def run(self):
        try:
            self.progress_callback(f"[任务{self.task_id}] 正在启动浏览器...")
            
            # 直接调用现有的登录脚本
            login_cdp = MeituanLoginCDP()
            
            self.progress_callback(f"[任务{self.task_id}] 请在弹出的浏览器中完成登录...")
            result = login_cdp.run()
            
            if result:
                # 添加代理信息到结果中
                result['agent_id'] = self.agent_info['id']
                result['agent_account'] = self.agent_info['agent_account']
                result['task_id'] = self.task_id
                
                self.progress_callback(f"[任务{self.task_id}] 登录成功，正在上传数据...")
                self.success_callback(result)
            else:
                self.error_callback(f"[任务{self.task_id}] 登录失败，请重试")
                
        except Exception as e:
            self.error_callback(f"[任务{self.task_id}] 登录过程出错: {str(e)}")

class UploadThread(threading.Thread):
    """上传线程"""
    def __init__(self, task_id, data, progress_callback, success_callback, error_callback):
        super().__init__()
        self.task_id = task_id
        self.data = data
        self.progress_callback = progress_callback
        self.success_callback = success_callback
        self.error_callback = error_callback
        self.daemon = True
    
    def run(self):
        try:
            self.progress_callback(f"[任务{self.task_id}] 正在上传店铺信息到后端...")
            
            # 调用后端接口
            api_url = f"{API_BASE}/api/shops/init"
            
            response = requests.post(
                api_url,
                json=self.data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['code'] == 0:
                    self.progress_callback(f"[任务{self.task_id}] 店铺信息上传成功！")
                    self.success_callback(result)
                else:
                    self.error_callback(f"[任务{self.task_id}] 上传失败: {result['message']}")
            else:
                self.error_callback(f"[任务{self.task_id}] 请求失败，状态码: {response.status_code}")
                
        except Exception as e:
            self.error_callback(f"[任务{self.task_id}] 上传过程出错: {str(e)}")

class ModernMeituanLoginGUI(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.agent_info = None
        self.active_tasks = {}  # 存储活跃任务 {task_id: thread}
        self.task_counter = 0   # 任务计数器
        self.session_file = "login_session.pkl"
        
        self.init_ui()
        # 延迟尝试自动登录，确保主窗口完全初始化
        self.after(100, self.try_auto_login)
        
    def init_ui(self):
        """初始化界面"""
        self.title("美团外卖登录器")
        self.geometry("600x500")
        self.resizable(False, False)
        
        # 主布局
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="美团外卖店铺登录器",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 代理信息显示
        self.agent_info_label = ctk.CTkLabel(
            main_frame,
            text="未登录",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.agent_info_label.pack(pady=(0, 20))
        
        # 说明文字
        desc_label = ctk.CTkLabel(
            main_frame,
            text="点击\"添加登录任务\"按钮，每次会打开一个新的浏览器窗口进行美团外卖商家登录。\n可以同时进行多个登录任务，每个任务独立运行。",
            font=ctk.CTkFont(size=12),
            text_color="gray",
            wraplength=500
        )
        desc_label.pack(pady=(0, 30))
        
        # 登录按钮
        self.login_btn = ctk.CTkButton(
            main_frame,
            text="+ 添加登录任务",
            width=200,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            command=self.start_login
        )
        self.login_btn.pack(pady=(0, 20))
        
        # 任务状态显示
        self.task_status_label = ctk.CTkLabel(
            main_frame,
            text="活跃任务: 0",
            font=ctk.CTkFont(size=12),
            text_color="blue"
        )
        self.task_status_label.pack(pady=(0, 10))
        
        # 进度条
        self.progress_bar = ctk.CTkProgressBar(main_frame)
        self.progress_bar.pack(fill="x", padx=50, pady=(0, 10))
        self.progress_bar.set(0)
        self.progress_bar.pack_forget()  # 初始隐藏
        
        # 状态显示
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="等待登录...",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.status_label.pack(pady=(0, 20))
        
        # 日志显示区域
        log_frame = ctk.CTkFrame(main_frame)
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        log_title = ctk.CTkLabel(
            log_frame,
            text="操作日志",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        log_title.pack(pady=10)
        
        self.log_text = ctk.CTkTextbox(
            log_frame,
            height=150,
            font=ctk.CTkFont(size=11)
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # 底部按钮区域
        bottom_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        bottom_frame.pack(fill="x", pady=10)
        
        self.clear_btn = ctk.CTkButton(
            bottom_frame,
            text="清空日志",
            width=100,
            height=35,
            command=self.clear_log,
            fg_color="gray"
        )
        self.clear_btn.pack(side="left")
        
        self.logout_btn = ctk.CTkButton(
            bottom_frame,
            text="退出登录",
            width=100,
            height=35,
            command=self.logout,
            fg_color="orange"
        )
        self.logout_btn.pack(side="left", padx=(10, 0))
        
        self.stop_all_btn = ctk.CTkButton(
            bottom_frame,
            text="停止所有任务",
            width=120,
            height=35,
            command=self.stop_all_tasks,
            fg_color="red"
        )
        self.stop_all_btn.pack(side="right")
        
        bottom_frame.pack_propagate(False)
        
        # 添加日志
        self.add_log("系统启动完成")
        self.add_log("请先登录代理账号")
        
        # 启动定期验证session的定时器（每30分钟验证一次）
        self.start_session_verification()
        
        # 启动定期更新任务状态的定时器（每2秒更新一次）
        self.start_task_status_update()
        
    def start_session_verification(self):
        """启动定期验证session - 简化版本，只要账号密码不变就不需要重新登录"""
        def verify_periodic():
            if self.agent_info and os.path.exists(self.session_file):
                try:
                    with open(self.session_file, 'rb') as f:
                        session_data = pickle.load(f)

                    # 检查账号密码是否发生变化
                    stored_account = session_data.get('agent_info', {}).get('agent_account', '')
                    stored_password = session_data.get('agent_info', {}).get('agent_password', '')
                    current_account = self.agent_info.get('agent_account', '')
                    current_password = self.agent_info.get('agent_password', '')

                    # 只有账号或密码发生变化时才需要重新验证
                    if stored_account != current_account or stored_password != current_password:
                        self.add_log("检测到账号或密码变化，需要重新登录")
                        # 删除失效的session文件
                        try:
                            os.remove(self.session_file)
                        except:
                            pass
                        # 重新显示登录对话框
                        self.after(1000, self.show_login_dialog)
                    else:
                        # 账号密码未变化，更新时间戳即可
                        self.save_session()
                        remaining_days = int((2592000 - (datetime.now().timestamp() - session_data.get('timestamp', 0))) / 86400)
                        self.add_log(f"账号密码未变化，session还有{remaining_days}天过期")

                except Exception as e:
                    self.add_log(f"验证session时出错: {str(e)}")

            # 2小时后再次验证（减少验证频率）
            self.after(7200000, verify_periodic)  # 2小时 = 7200000毫秒

        # 启动第一次验证（延迟2小时）
        self.after(7200000, verify_periodic)
        
    def start_task_status_update(self):
        """启动定期更新任务状态"""
        def update_periodic():
            self.update_task_status()
            # 2秒后再次更新
            self.after(2000, update_periodic)
            
        # 启动第一次更新
        self.after(2000, update_periodic)
        
    def try_auto_login(self):
        """尝试自动登录"""
        if os.path.exists(self.session_file):
            try:
                with open(self.session_file, 'rb') as f:
                    session_data = pickle.load(f)
                    
                # 检查session是否过期（30天）
                if datetime.now().timestamp() - session_data.get('timestamp', 0) < 2592000:  # 30天 = 30 * 24 * 60 * 60 = 2592000秒
                    self.agent_info = session_data.get('agent_info')
                    if self.agent_info and self.agent_info.get('agent_password'):
                        # 简化验证：只要session未过期且数据完整就直接使用
                        self.agent_info_label.configure(
                            text=f"当前代理: {self.agent_info['agent_account']}",
                            text_color="green"
                        )
                        remaining_days = int((2592000 - (datetime.now().timestamp() - session_data.get('timestamp', 0))) / 86400)
                        self.add_log(f"自动登录成功: {self.agent_info['agent_account']} (还有{remaining_days}天过期)")
                        return
                    else:
                        self.add_log("session数据不完整，需要重新登录")
                        # 删除不完整的session文件
                        try:
                            os.remove(self.session_file)
                        except:
                            pass
                else:
                    self.add_log("session已过期，需要重新登录")
                    # 删除过期的session文件
                    try:
                        os.remove(self.session_file)
                    except:
                        pass
                        
            except Exception as e:
                self.add_log(f"读取登录状态失败: {str(e)}")
                
        # 如果没有有效的session，显示登录对话框
        self.show_login_dialog()
        
    def verify_session(self):
        """验证session有效性"""
        try:
            # 调用验证接口验证账号密码
            response = requests.post(
                f"{API_BASE}/api/auth/verify",
                json={
                    'agent_account': self.agent_info['agent_account'],
                    'agent_password': self.agent_info.get('agent_password', '')
                },
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            result = response.json()
            if result['code'] == 0:
                # 更新最新的代理信息
                self.agent_info = result['data']['agent']
                return True
            else:
                self.add_log(f"验证失败: {result['message']}")
                return False

        except Exception as e:
            self.add_log(f"验证session失败: {str(e)}")
            return False
        
    def show_login_dialog(self):
        """显示登录对话框"""
        login_dialog = LoginDialog(self)
        self.wait_window(login_dialog)
        
        if login_dialog.result and login_dialog.agent_info:
            self.agent_info = login_dialog.agent_info
            self.agent_info_label.configure(
                text=f"当前代理: {self.agent_info['agent_account']}",
                text_color="green"
            )
            self.add_log(f"代理登录成功: {self.agent_info['agent_account']}")
            self.save_session()
        else:
            # 如果没有登录成功，退出程序
            self.after(1000, self.quit)
            
    def save_session(self):
        """保存登录状态"""
        try:
            # 需要保存密码用于后续验证
            session_data = {
                'agent_info': self.agent_info,
                'timestamp': datetime.now().timestamp()
            }
            with open(self.session_file, 'wb') as f:
                pickle.dump(session_data, f)
        except Exception as e:
            self.add_log(f"保存登录状态失败: {str(e)}")
            
    def logout(self):
        """退出登录"""
        if os.path.exists(self.session_file):
            try:
                os.remove(self.session_file)
            except:
                pass
                
        self.agent_info = None
        self.agent_info_label.configure(text="未登录", text_color="gray")
        self.add_log("已退出登录")
        
        # 重新显示登录对话框
        self.show_login_dialog()
        
    def start_login(self):
        """开始登录 - 每次点击增加一个登录任务"""
        # 如果还没有登录代理账号，先显示登录对话框
        if not self.agent_info:
            self.show_login_dialog()
            return
        
        # 生成新的任务ID
        self.task_counter += 1
        task_id = self.task_counter
        
        # 启动登录线程
        login_thread = LoginThread(task_id, self.agent_info, self.update_progress, self.on_login_success, self.on_login_error)
        login_thread.start()
        self.active_tasks[task_id] = login_thread
        
        # 更新任务状态
        self.update_task_status()
        self.add_log(f"已启动任务 {task_id}，当前活跃任务: {len(self.active_tasks)}")
        
    def update_task_status(self):
        """更新任务状态显示"""
        active_count = len([t for t in self.active_tasks.values() if t.is_alive()])
        total_count = len(self.active_tasks)
        
        if total_count > 0:
            status_text = f"活跃任务: {active_count}/{total_count}"
            if active_count > 0:
                self.task_status_label.configure(text=status_text, text_color="green")
            else:
                self.task_status_label.configure(text=status_text, text_color="gray")
        else:
            self.task_status_label.configure(text="活跃任务: 0", text_color="blue")
        
        # 清理已完成的任务
        completed_tasks = [task_id for task_id, thread in self.active_tasks.items() if not thread.is_alive()]
        for task_id in completed_tasks:
            del self.active_tasks[task_id]
        
    def update_progress(self, message):
        """更新进度信息"""
        self.after(0, lambda: self.status_label.configure(text=message, text_color="blue"))
        self.after(0, lambda: self.add_log(message))
        self.after(0, lambda: self.update_task_status())
        
    def stop_all_tasks(self):
        """停止所有任务"""
        if not self.active_tasks:
            messagebox.showinfo("提示", "当前没有活跃任务")
            return
            
        if messagebox.askyesno("确认停止", "确定要停止所有正在进行的任务吗？"):
            # 清理所有任务
            self.active_tasks.clear()
            self.update_task_status()
            self.add_log("已停止所有任务")
            
    def on_login_success(self, result):
        """登录成功回调"""
        task_id = result.get('task_id', '未知')
        self.after(0, lambda: self.add_log(f"[任务{task_id}] 登录成功，获取到店铺信息"))
        self.after(0, lambda: self.add_log(f"[任务{task_id}] 店铺名称: {result.get('shop_name', '未知')}"))
        self.after(0, lambda: self.add_log(f"[任务{task_id}] 登录账号: {result.get('login_account', '未知')}"))
        self.after(0, lambda: self.add_log(f"[任务{task_id}] 绑定代理: {result.get('agent_account', '未知')}"))
        
        # 启动上传线程
        upload_thread = UploadThread(task_id, result, self.update_progress, self.on_upload_success, self.on_upload_error)
        upload_thread.start()
        self.active_tasks[task_id] = upload_thread
        
    def on_login_error(self, error):
        """登录失败回调"""
        self.after(0, lambda: self.add_log(f"登录失败: {error}"))
        self.after(0, lambda: self.status_label.configure(text="登录失败", text_color="red"))
        self.after(0, lambda: self.update_task_status())
        
    def on_upload_success(self, result):
        """上传成功回调"""
        task_id = result.get('task_id', '未知')
        self.after(0, lambda: self.add_log(f"[任务{task_id}] 店铺信息上传成功！"))
        self.after(0, lambda: self.add_log(f"[任务{task_id}] 店铺ID: {result['data']['shop_id']}"))
        self.after(0, lambda: self.add_log(f"[任务{task_id}] 任务完成"))
        self.after(0, lambda: self.status_label.configure(text="上传成功", text_color="green"))
        self.after(0, lambda: self.update_task_status())
        
    def on_upload_error(self, error):
        """上传失败回调"""
        self.after(0, lambda: self.add_log(f"上传失败: {error}"))
        self.after(0, lambda: self.status_label.configure(text="上传失败", text_color="red"))
        self.after(0, lambda: self.update_task_status())
        
    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete("1.0", "end")
        self.add_log("日志已清空")
        
    def on_closing(self):
        """关闭事件"""
        if self.active_tasks:
            active_count = len([t for t in self.active_tasks.values() if t.is_alive()])
            if active_count > 0:
                if messagebox.askokcancel("确认退出", f"还有 {active_count} 个登录任务正在进行中，确定要退出吗？"):
                    self.quit()
            else:
                self.quit()
        else:
            self.quit()

if __name__ == "__main__":
    app = ModernMeituanLoginGUI()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop() 