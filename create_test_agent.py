#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建测试代理账号
"""

import hashlib
from utils.db_utils import get_connection

def create_test_agent():
    """创建测试代理账号"""
    connection = None
    cursor = None
    try:
        # 测试账号信息
        agent_account = "test"
        agent_password = "123456"
        
        # 密码加密
        password_hash = hashlib.md5(agent_password.encode('utf-8')).hexdigest()
        print(f"创建测试账号: {agent_account}")
        print(f"密码: {agent_password}")
        print(f"密码哈希: {password_hash}")
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查账号是否已存在
        check_query = "SELECT id FROM agents WHERE agent_account = %s"
        cursor.execute(check_query, [agent_account])
        existing_agent = cursor.fetchone()
        
        if existing_agent:
            print("测试账号已存在，删除旧账号...")
            delete_query = "DELETE FROM agents WHERE agent_account = %s"
            cursor.execute(delete_query, [agent_account])
        
        # 创建新代理
        insert_query = """
        INSERT INTO agents (agent_account, agent_password, role, balance, created_at, updated_at)
        VALUES (%s, %s, %s, %s, NOW(), NOW())
        """
        
        cursor.execute(insert_query, [
            agent_account,
            password_hash,
            'user',
            0
        ])
        
        connection.commit()
        print("测试代理账号创建成功！")
        
        # 验证创建结果
        cursor.execute("SELECT * FROM agents WHERE agent_account = %s", [agent_account])
        agent = cursor.fetchone()
        print(f"创建的代理信息: {agent}")
        
    except Exception as e:
        print(f"创建测试代理失败: {str(e)}")
        if connection:
            connection.rollback()
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    create_test_agent()
