#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
美团外卖助手后端主应用
"""

from flask import Flask
from flask_cors import CORS
from config import DB_CONFIG
import os
from datetime import timedelta

def create_app():
    app = Flask(__name__)
    
    # 配置session密钥
    app.secret_key = os.environ.get('SECRET_KEY', 'your-secret-key-here')
    # session 配置
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # 允许跨站携带 cookie
    app.config['SESSION_COOKIE_SECURE'] = False    # 开发环境关闭 https 要求
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=30)  # 设置session过期时间为30天
    # 允许跨域请求
    CORS(app, supports_credentials=True)
    
    # 注册蓝图
    from routes.shop_routes import shop_bp
    from routes.agent_routes import agent_bp
    from routes.task_routes import task_bp
    from routes.auth_routes import auth_bp
    from routes.admin_routes import admin_bp
    
    app.register_blueprint(shop_bp, url_prefix='/api/shops')
    app.register_blueprint(agent_bp, url_prefix='/api/agents')
    app.register_blueprint(task_bp, url_prefix='/api/tasks')
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    
    @app.route('/')
    def index():
        return {'message': '美团外卖助手后端服务运行中'}
    
    @app.route('/health')
    def health():
        return {'status': 'ok', 'message': '服务正常'}
    
    return app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000) 