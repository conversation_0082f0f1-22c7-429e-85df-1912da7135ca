#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试登录检测功能
"""

from meituan_login_cdp import MeituanLoginCDP

def test_login_detection():
    """测试登录检测功能"""
    print("🚀 开始测试登录检测功能...")
    print("📝 请在弹出的浏览器中完成登录操作")
    print("🔍 程序会自动检测登录状态变化")
    
    try:
        login_cdp = MeituanLoginCDP()
        result = login_cdp.run()
        
        if result:
            print("\n🎉 测试成功！获取到完整数据:")
            print(f"  店铺ID: {result.get('shop_id', '未知')}")
            print(f"  店铺名称: {result.get('shop_name', '未知')}")
            print(f"  登录账号: {result.get('login_account', '未知')}")
            print(f"  Logo: {result.get('logo', '无')}")
            print(f"  Cookie长度: {len(result.get('cookie', ''))}")
        else:
            print("❌ 测试失败，未能获取到数据")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    test_login_detection()
