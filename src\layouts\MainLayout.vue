<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const isCollapse = ref(false)
const router = useRouter()

const handleSelect = (key: string) => {
  router.push(key)
}

// 获取用户信息
const userInfo = computed(() => {
  const info = localStorage.getItem('userInfo')
  return info ? JSON.parse(info) : null
})

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const result = await response.json()

    if (result.code === 0) {
      // 清除本地存储
      localStorage.removeItem('userInfo')
      localStorage.removeItem('token')

      ElMessage.success('退出成功')
      // 跳转到登录页
      router.push('/login')
    } else {
      ElMessage.error(result.message || '退出失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录错误:', error)
      ElMessage.error('退出失败')
    }
  }
}
</script>

<template>
  <div class="main-layout">
    <el-container class="layout-container">
      <!-- 头部 -->
      <el-header class="layout-header">
        <div class="header-left">
          <el-button
            type="primary"
            class="collapse-btn"
            :icon="isCollapse ? 'Expand' : 'Fold'"
            @click="isCollapse = !isCollapse"
          />
          <h2 class="system-title">外卖助手</h2>
        </div>
        <div class="header-right">
          <el-dropdown>
            <span class="user-info">
              <el-avatar :size="36" class="user-avatar">
                {{ userInfo?.agent_account?.charAt(0)?.toUpperCase() || '代' }}
              </el-avatar>
              <span class="username">{{ userInfo?.agent_account || '代理' }}</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="router.push('/account')">
                  <el-icon><User /></el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item @click="handleLogout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-container class="main-container">
        <!-- 侧边栏 -->
        <el-aside :width="isCollapse ? '64px' : '220px'" class="layout-aside">
          <el-menu
            :collapse="isCollapse"
            :collapse-transition="false"
            background-color="#001529"
            text-color="#fff"
            active-text-color="#409eff"
            :default-active="$route.path"
            @select="handleSelect"
            class="sidebar-menu"
          >
            <div class="sidebar-title" v-if="!isCollapse">
              <el-icon><Menu /></el-icon>
              <span>功能导航</span>
            </div>
            <el-menu-item index="/meituan" class="menu-item">
              <el-icon><ShoppingCart /></el-icon>
              <template #title>
                <span class="menu-text">美团</span>
              </template>
            </el-menu-item>
            <el-menu-item index="/account" class="menu-item">
              <el-icon><User /></el-icon>
              <template #title>
                <span class="menu-text">账号管理</span>
              </template>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="layout-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style>
.el-container,
.el-header,
.el-main,
.el-aside {
  padding: 0;
  margin: 0;
}

.el-container {
  width: 100vw;
  height: 100vh;
}

.el-dropdown-item .el-icon {
  margin-right: 8px;
}
</style>

<style scoped>
.main-layout {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
}

.layout-container {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
}

.main-container {
  width: 100vw;
  height: calc(100vh - 60px);
}

.layout-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  width: 100vw;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.collapse-btn {
  border-radius: 4px;
  transition: all 0.3s;
}

.collapse-btn:hover {
  transform: scale(1.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.system-title {
  margin-left: 15px;
  font-size: 20px;
  font-weight: bold;
  color: #001529;
  background-image: linear-gradient(45deg, #1890ff, #52c41a);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.user-avatar {
  background: linear-gradient(135deg, #1890ff, #52c41a);
  color: white;
  font-weight: bold;
}

.username {
  margin-left: 8px;
  font-size: 16px;
  color: #333;
}

.layout-aside {
  background-color: #001529;
  transition: width 0.3s;
  overflow: hidden;
  height: calc(100vh - 60px);
  min-height: calc(100vh - 60px);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.sidebar-title {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.menu-item {
  height: 56px !important;
  line-height: 56px !important;
}

.menu-text {
  font-size: 16px;
}

:deep(.el-menu-item) {
  height: 56px !important;
  line-height: 56px !important;
}

:deep(.el-menu-item .el-icon) {
  font-size: 18px !important;
}

.layout-main {
  background-color: #f0f2f5;
  padding: 20px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  flex: 1;
}
</style>
