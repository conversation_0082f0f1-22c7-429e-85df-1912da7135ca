<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
  <RouterView />
</template>

<style>
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  font-family:
    'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial,
    sans-serif;
  overflow: hidden;
  color: #333;
}

body {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f0f2f5;
}

#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.el-container {
  width: 100vw !important;
  max-width: 100vw !important;
  min-width: 100vw !important;
}

/* 全局按钮动画效果 */
.el-button {
  transition: all 0.3s;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 卡片阴影效果 */
.el-card {
  transition: all 0.3s;
  border: none !important;
}

.el-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* 表格样式优化 */
.el-table th {
  background-color: #f5f7fa !important;
  color: #333 !important;
  font-weight: 600 !important;
}

/* 分页样式优化 */
.el-pagination {
  margin-top: 16px;
  justify-content: flex-end;
}

/* 图标样式 */
.el-icon {
  vertical-align: middle;
}

/* 表单元素样式 */
.el-input__inner,
.el-select__inner {
  border-radius: 4px !important;
}

/* 下拉菜单样式 */
.el-dropdown-menu {
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
</style>
