#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试登录接口
"""

import requests
import json

def test_login():
    """测试登录接口"""
    api_base = "http://localhost:5000"
    
    # 测试数据
    login_data = {
        'agent_account': 'test',
        'agent_password': '123456'
    }
    
    try:
        print("测试登录接口...")
        print(f"请求数据: {login_data}")
        
        response = requests.post(
            f"{api_base}/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result['code'] == 0:
                print("✅ 登录成功！")
                print(f"代理信息: {result['data']['agent']['agent_account']}")
                print(f"Token: {result['data']['token']}")
            else:
                print(f"❌ 登录失败: {result['message']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == "__main__":
    test_login()
