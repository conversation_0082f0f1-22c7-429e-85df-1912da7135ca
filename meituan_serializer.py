#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
美团WebSocket消息序列化工具

实现与美团官方一致的二进制序列化功能，包括：
1. 认证信息序列化
2. 请求消息序列化
3. TransUp消息序列化
"""

import json
import argparse
from meituan_buffer import ByteBuffer, array_buffer_to_base64, hex_dump, array_buffer_to_hex_string
from meituan_packet import TextMessage, PubSendMsgKFReq, TransUp, AuthPacket

def serialize_request(request_data):
    """
    序列化请求对象
    
    Args:
        request_data: 请求对象数据
        
    Returns:
        dict: 序列化结果，包含bytes, base64, byteLength
    """
    # 创建消息对象
    message_data = request_data.get('message', {})
    message = TextMessage(message_data)
    
    # 创建请求对象并设置消息
    request = PubSendMsgKFReq(request_data)
    # 使用setattr解决类型错误
    setattr(request, 'message', message)
    
    # 获取序列化后的二进制数据
    bytes_data = request.get_bytes()
    
    # 转换为Base64
    base64_str = array_buffer_to_base64(bytes_data)
    
    return {
        'bytes': bytes_data,
        'base64': base64_str,
        'byteLength': len(bytes_data)
    }

def serialize_trans_up(request_data, trans_up_data=None):
    """
    序列化TransUp对象
    
    Args:
        request_data: 请求对象数据
        trans_up_data: TransUp对象数据
        
    Returns:
        dict: 序列化结果，包含bytes, base64, byteLength, requestResult
    """
    # 先序列化request对象
    request_result = serialize_request(request_data)
    
    # 创建TransUp对象
    trans_up = TransUp(trans_up_data or {})
    trans_up.buf = request_result['bytes']  # 使用request序列化结果作为buf
    
    # 获取序列化后的二进制数据
    bytes_data = trans_up.get_bytes()
    
    # 转换为Base64
    base64_str = array_buffer_to_base64(bytes_data)
    
    return {
        'bytes': bytes_data,
        'base64': base64_str,
        'byteLength': len(bytes_data),
        'requestResult': request_result  # 包含原始request序列化结果
    }

def serialize_auth(auth_data):
    """
    序列化认证数据
    
    Args:
        auth_data: 认证数据
        
    Returns:
        dict: 序列化结果，包含bytes, base64, byteLength
    """
    # 创建认证对象前确保版本属性正确
    if 'version' in auth_data and isinstance(auth_data['version'], str):
        # 保存字符串版本号
        auth_data['version_str'] = auth_data['version']
        # 设置整数版本号为1
        auth_data['version'] = 1
    
    # 创建认证对象
    auth = AuthPacket(auth_data)
    
    # 获取序列化后的二进制数据
    bytes_data = auth.get_bytes()
    
    # 转换为Base64
    base64_str = array_buffer_to_base64(bytes_data)
    
    return {
        'bytes': bytes_data,
        'base64': base64_str,
        'byteLength': len(bytes_data)
    }

def compare_with_official(our_bytes, official_base64):
    """
    比较我们的序列化结果与官方结果
    
    Args:
        our_bytes: 我们序列化的字节数组
        official_base64: 官方序列化结果的Base64编码
        
    Returns:
        dict: 比较结果
    """
    from meituan_buffer import base64_to_array_buffer
    
    try:
        official_bytes = base64_to_array_buffer(official_base64)
        
        result = {
            'our_length': len(our_bytes),
            'official_length': len(official_bytes),
            'length_match': len(our_bytes) == len(official_bytes),
            'differences': []
        }
        
        # 详细比较
        min_length = min(len(our_bytes), len(official_bytes))
        
        for i in range(min_length):
            if our_bytes[i] != official_bytes[i]:
                # 使用字典存储差异信息
                result['differences'].append({
                    'position': i,
                    'our_value': our_bytes[i],
                    'official_value': official_bytes[i]
                })
                if len(result['differences']) >= 10:
                    break
        
        result['content_match'] = len(result['differences']) == 0 and result['length_match']
        
        return result
    except Exception as e:
        return {
            'error': str(e)
        }

def print_serialization_result(result, official_result=None):
    """
    打印序列化结果
    
    Args:
        result: 序列化结果
        official_result: 官方序列化结果的Base64编码
    """
    print("\n===== 序列化结果 =====")
    print(f"字节长度: {result['byteLength']}")
    print("\nBase64:")
    print(result['base64'])
    
    print("\n十六进制字符串:")
    print(array_buffer_to_hex_string(result['bytes']))
    
    print("\n十六进制转储:")
    print(hex_dump(result['bytes']))
    
    if official_result:
        comparison = compare_with_official(result['bytes'], official_result)
        
        print("\n===== 与官方结果比较 =====")
        if 'error' in comparison:
            print(f"比较出错: {comparison['error']}")
            return
        
        print(f"我们的长度: {comparison['our_length']}")
        print(f"官方长度: {comparison['official_length']}")
        print(f"长度匹配: {'是' if comparison['length_match'] else '否'}")
        
        if comparison['differences']:
            print(f"\n前{len(comparison['differences'])}个差异:")
            for diff in comparison['differences']:
                # 确保diff是字典类型
                if isinstance(diff, dict):
                    position = diff.get('position', 0)
                    our_value = diff.get('our_value', 0)
                    official_value = diff.get('official_value', 0)
                    print(f"位置 {position}: 我们={our_value}, 官方={official_value}")
                else:
                    print(f"差异数据: {diff}")
        elif comparison['content_match']:
            print("\n内容完全相同!")

def main():
    parser = argparse.ArgumentParser(description='美团WebSocket消息序列化工具')
    parser.add_argument('mode', choices=['request', 'transup', 'auth'], help='序列化模式')
    parser.add_argument('--input', '-i', required=True, help='输入JSON文件路径')
    parser.add_argument('--transup', '-t', help='TransUp JSON文件路径（仅在transup模式下使用）')
    parser.add_argument('--official', '-o', help='官方序列化结果Base64文件路径（用于比较）')
    parser.add_argument('--output', help='输出结果文件路径')
    
    args = parser.parse_args()
    
    # 读取输入JSON
    with open(args.input, 'r', encoding='utf-8') as f:
        input_data = json.load(f)
    
    # 读取TransUp JSON（如果有）
    trans_up_data = None
    if args.transup and args.mode == 'transup':
        with open(args.transup, 'r', encoding='utf-8') as f:
            trans_up_data = json.load(f)
    
    # 读取官方序列化结果（如果有）
    official_result = None
    if args.official:
        with open(args.official, 'r', encoding='utf-8') as f:
            official_result = f.read().strip()
    
    # 执行序列化
    if args.mode == 'request':
        result = serialize_request(input_data)
    elif args.mode == 'transup':
        result = serialize_trans_up(input_data, trans_up_data)
    elif args.mode == 'auth':
        result = serialize_auth(input_data)
    
    # 打印结果
    print_serialization_result(result, official_result)
    
    # 保存结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(result['base64'])
        print(f"\n结果已保存到: {args.output}")

if __name__ == "__main__":
    main() 